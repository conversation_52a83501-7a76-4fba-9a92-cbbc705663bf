<?xml version="1.0"?>
<?xml-stylesheet type='text/xsl' href='MessageLog.xsl'?>
<Log FirstSessionID="1" LastSessionID="4"><Message Date="15.10.2008" Time="21:38:18" DateTime="2008-10-15T19:38:18.468Z" SessionID="1"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Lucida <PERSON>sole; font-weight:bold; color:#000000; ">(Y) No problem, I'll have it for you later on, I sent you a message on the message board</Text></Message><Message Date="15.10.2008" Time="21:38:28" DateTime="2008-10-15T19:38:28.015Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Great, thanks</Text></Message><Message Date="16.06.2009" Time="22:29:51" DateTime="2009-06-16T20:29:51.593Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Hello, I remember your name from a project a scriptlance some time ago - you're a wiz at C#, right :)</Text></Message><Message Date="16.06.2009" Time="22:30:05" DateTime="2009-06-16T20:30:05.796Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">yea, how are ud oing?</Text></Message><Message Date="16.06.2009" Time="22:30:11" DateTime="2009-06-16T20:30:11.875Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">you*</Text></Message><Message Date="16.06.2009" Time="22:31:25" DateTime="2009-06-16T20:31:25.187Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">It's all good, and you? (all except the C# part btw) :P</Text></Message><Message Date="16.06.2009" Time="22:32:22" DateTime="2009-06-16T20:32:22.546Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Just thought I'd notify you about a little project, in case you're interested.. I don't have the patience to learn programming, I think</Text></Message><Message Date="16.06.2009" Time="22:32:32" DateTime="2009-06-16T20:32:32.812Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">https://www.scriptlance.com/cgi-bin/freelancers/project.cgi?id=1245183042</Text></Message><Message Date="16.06.2009" Time="22:33:25" DateTime="2009-06-16T20:33:25.796Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I've spendt two hours on trying to list data from a sql table to a listview control :P</Text></Message><Message Date="16.06.2009" Time="23:14:27" DateTime="2009-06-16T21:14:27.453Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">thats easy</Text></Message><Message Date="16.06.2009" Time="23:14:28" DateTime="2009-06-16T21:14:28.046Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">:P</Text></Message><Message Date="16.06.2009" Time="23:15:47" DateTime="2009-06-16T21:15:47.968Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">heh, everything's easy if you know how to do it ;) how much would you want for doing the job?</Text></Message><Message Date="16.06.2009" Time="23:19:39" DateTime="2009-06-16T21:19:39.140Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Serif; font-weight:bold; color:#000000; ">Well, you just want a basic interface for this? listing the tables, add/edit/delete functionality righT?</Text></Message><Message Date="16.06.2009" Time="23:20:08" DateTime="2009-06-16T21:20:08.203Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, I think that's about it</Text></Message><Message Date="16.06.2009" Time="23:20:40" DateTime="2009-06-16T21:20:40.671Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I've thought about making the sum of the columns 100%, but I don't know if that's hard to accomplish?</Text></Message><Message Date="16.06.2009" Time="23:20:54" DateTime="2009-06-16T21:20:54.234Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">by sum, I mean width</Text></Message><Message Date="16.06.2009" Time="23:20:58" DateTime="2009-06-16T21:20:58.906Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">no it's not</Text></Message><Message Date="16.06.2009" Time="23:21:05" DateTime="2009-06-16T21:21:05.843Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">how about $120?</Text></Message><Message Date="16.06.2009" Time="23:21:24" DateTime="2009-06-16T21:21:24.750Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">I don't really go on Scriptlance anymore cause i work full time during the day and i am quite busy</Text></Message><Message Date="16.06.2009" Time="23:21:36" DateTime="2009-06-16T21:21:36.140Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">but what's better is to work w/ me direct via paypal</Text></Message><Message Date="16.06.2009" Time="23:21:55" DateTime="2009-06-16T21:21:55.906Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">better yet, $100 is fine</Text></Message><Message Date="16.06.2009" Time="23:25:20" DateTime="2009-06-16T21:25:20.390Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I see, I already got a bid on 80$ on scriptlance, I wasn't planning to spend too much on it - I already got the working application in VB, but the reason why I want it in C# is mainly because of curiosity/learning purposes :) But I understand that you don't want to go to low if it's timeconsuming, and you're already in a fulltime job :)</Text></Message><Message Date="16.06.2009" Time="23:25:55" DateTime="2009-06-16T21:25:55.781Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">Well, I am full time lead engineer, go to class during evening (not anymore but soon i will be teaching C)</Text></Message><Message Date="16.06.2009" Time="23:26:07" DateTime="2009-06-16T21:26:07.156Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">and i am working on my own new website for my own project management site</Text></Message><Message Date="16.06.2009" Time="23:26:30" DateTime="2009-06-16T21:26:30.609Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">So ok.. i see where your going, $80 will be the lowest though</Text></Message><Message Date="16.06.2009" Time="23:26:47" DateTime="2009-06-16T21:26:47.703Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">and the $$ isn't for me, it's going to be for a friend of mine who doesn't work</Text></Message><Message Date="16.06.2009" Time="23:26:57" DateTime="2009-06-16T21:26:57.703Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">but who helps me w/ graphics design</Text></Message><Message Date="16.06.2009" Time="23:27:06" DateTime="2009-06-16T21:27:06.890Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">so if that's cool w/ u, it works for me</Text></Message><Message Date="16.06.2009" Time="23:38:58" DateTime="2009-06-16T21:38:58.156Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">An engineer huh, I guess you're kinda overqualified :P I wasn't actually trying to get you to go lower, but at first I thought 80$ was a bit high (higher than what I thought) - But I'll meet you at 80$, atleast then I know I get good code ;)</Text></Message><Message Date="16.06.2009" Time="23:41:04" DateTime="2009-06-16T21:41:04.546Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">:) I'm not a lead for nothing lol</Text></Message><Message Date="16.06.2009" Time="23:42:07" DateTime="2009-06-16T21:42:07.468Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">I can have it delivered to you by tmmw morning early, i'm in California</Text></Message><Message Date="16.06.2009" Time="23:42:49" DateTime="2009-06-16T21:42:49.609Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe, sorry - lead engineer :) Do you want me to transfere 40$ now, then 40$ after... or something?</Text></Message><Message Date="16.06.2009" Time="23:43:36" DateTime="2009-06-16T21:43:36.968Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">sec</Text></Message><Message Date="16.06.2009" Time="23:44:35" DateTime="2009-06-16T21:44:35.437Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">ok, send it via <NAME_EMAIL></Text></Message><Message Date="16.06.2009" Time="23:45:06" DateTime="2009-06-16T21:45:06.343Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">and is this e-mail the one I may reach u at? <EMAIL>?</Text></Message><Message Date="16.06.2009" Time="23:45:56" DateTime="2009-06-16T21:45:56.765Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">That's right</Text></Message><Message Date="16.06.2009" Time="23:46:32" DateTime="2009-06-16T21:46:32.578Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">Cool, send to my e-mail again, same like paypal address, everything that i need to connect to ur database, the table u want to administrate</Text></Message><Message Date="16.06.2009" Time="23:46:45" DateTime="2009-06-16T21:46:45.250Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">and any other info i might need, and if possible ,a  contact # in-case I have any questions</Text></Message><Message Date="16.06.2009" Time="23:47:49" DateTime="2009-06-16T21:47:49.359Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I've sendt 40$ to your paypal now, I'll send you the project description in a couple of minutes</Text></Message><Message Date="16.06.2009" Time="23:48:07" DateTime="2009-06-16T21:48:07.859Z" SessionID="2"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">ok cool, I will check it later on after I leave work</Text></Message><Message Date="17.06.2009" Time="18:32:38" DateTime="2009-06-17T16:32:38.640Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">sup, good morning</Text></Message><Message Date="17.06.2009" Time="18:32:54" DateTime="2009-06-17T16:32:54.921Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, good evening :)</Text></Message><Message Date="17.06.2009" Time="18:33:01" DateTime="2009-06-17T16:33:01.515Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">:) hehe, where are u at again?</Text></Message><Message Date="17.06.2009" Time="18:33:16" DateTime="2009-06-17T16:33:16.500Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">norway, 18:30 pm</Text></Message><Invitation Date="17.06.2009" Time="18:34:01" DateTime="2009-06-17T16:34:01.890Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><File>jh_example.png</File><Text Style="color:#545454; ">+TuAnKoI sender jh_example.png</Text></Invitation><Message Date="17.06.2009" Time="18:34:03" DateTime="2009-06-17T16:34:03.734Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">Cool</Text></Message><Message Date="17.06.2009" Time="18:34:05" DateTime="2009-06-17T16:34:05.375Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">take a look</Text></Message><InvitationResponse Date="17.06.2009" Time="18:34:25" DateTime="2009-06-17T16:34:25.203Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><File>D:\Privat\Dokumenter\Mine mottatte filer\jh_example.png</File><Text Style="color:#545454; ">Du har mottatt D:\Privat\Dokumenter\Mine mottatte filer\jh_example.png fra +TuAnKoI.</Text></InvitationResponse><Message Date="17.06.2009" Time="18:35:02" DateTime="2009-06-17T16:35:02.156Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">looks very nice</Text></Message><Message Date="17.06.2009" Time="18:35:08" DateTime="2009-06-17T16:35:08.921Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">does it work? :P</Text></Message><Message Date="17.06.2009" Time="18:35:31" DateTime="2009-06-17T16:35:31.906Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">Smooth ;)</Text></Message><Message Date="17.06.2009" Time="18:35:35" DateTime="2009-06-17T16:35:35.078Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">I'm going to send it now</Text></Message><Message Date="17.06.2009" Time="18:35:36" DateTime="2009-06-17T16:35:36.250Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">1sec</Text></Message><Message Date="17.06.2009" Time="18:35:45" DateTime="2009-06-17T16:35:45.156Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">just finished up actually, i am testing search</Text></Message><Message Date="17.06.2009" Time="18:35:51" DateTime="2009-06-17T16:35:51.281Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">then off to work</Text></Message><Message Date="17.06.2009" Time="18:36:36" DateTime="2009-06-17T16:36:36.031Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">great, do you start work 12:30 pm btw?</Text></Message><Message Date="17.06.2009" Time="18:37:11" DateTime="2009-06-17T16:37:11.015Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">well here in California it's 9:36am, I can start from 7am or latest 11am</Text></Message><Message Date="17.06.2009" Time="18:37:29" DateTime="2009-06-17T16:37:29.875Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">8-hour work days, yesterday i was off at 3;30pm but today probably between 5-6pm</Text></Message><Message Date="17.06.2009" Time="18:38:41" DateTime="2009-06-17T16:38:41.859Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah, tried out the time wolframalpha time-thingy</Text></Message><Message Date="17.06.2009" Time="18:41:04" DateTime="2009-06-17T16:41:04.312Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">btw did you put on some comments in the code?</Text></Message><Message Date="17.06.2009" Time="18:41:33" DateTime="2009-06-17T16:41:33.234Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">yep</Text></Message><Message Date="17.06.2009" Time="18:42:42" DateTime="2009-06-17T16:42:42.281Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice</Text></Message><Message Date="17.06.2009" Time="18:44:37" DateTime="2009-06-17T16:44:37.640Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">i sent source to ur e-mail</Text></Message><Message Date="17.06.2009" Time="18:44:52" DateTime="2009-06-17T16:44:52.625Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">let me know when you've received it</Text></Message><Invitation Date="17.06.2009" Time="18:45:42" DateTime="2009-06-17T16:45:42.187Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><File>Src.txt</File><Text Style="color:#545454; ">+TuAnKoI sender Src.txt</Text></Invitation><Message Date="17.06.2009" Time="18:45:48" DateTime="2009-06-17T16:45:48.406Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">rename it to a .zip when its finished</Text></Message><Message Date="17.06.2009" Time="18:45:59" DateTime="2009-06-17T16:45:59.125Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">i got a mail delivery error</Text></Message><Message Date="17.06.2009" Time="18:46:23" DateTime="2009-06-17T16:46:23.765Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah :)</Text></Message><InvitationResponse Date="17.06.2009" Time="18:46:37" DateTime="2009-06-17T16:46:37.328Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\Src.txt</File><Text Style="color:#545454; ">Du har mottatt C:\Documents and Settings\Administrator\Skrivebord\Src.txt fra +TuAnKoI.</Text></InvitationResponse><Message Date="17.06.2009" Time="18:46:38" DateTime="2009-06-17T16:46:38.359Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">I used one of my model builders from work</Text></Message><Message Date="17.06.2009" Time="18:46:42" DateTime="2009-06-17T16:46:42.312Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">since it has a cleaner structure</Text></Message><Message Date="17.06.2009" Time="18:47:02" DateTime="2009-06-17T16:47:02.875Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">you'll like it, it utilizes Generics fully and Lambda expressions which is very efficient</Text></Message><Message Date="17.06.2009" Time="18:48:48" DateTime="2009-06-17T16:48:48.828Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">just a heads up, you may have to go into the project settings (located in the properties folder, Settings.settings) and change the path of the local database</Text></Message><Message Date="17.06.2009" Time="18:51:45" DateTime="2009-06-17T16:51:45.546Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Sweet, I'll have a look at it now :) I see it's done in a different way than how I tried to do this, I wrote all of the code in the Form.cs</Text></Message><Message Date="17.06.2009" Time="18:52:01" DateTime="2009-06-17T16:52:01.828Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">They don't call me lead for nothing ;)</Text></Message><Message Date="17.06.2009" Time="18:52:30" DateTime="2009-06-17T16:52:30.843Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe :) What is the app.config for btw? </Text></Message><Message Date="17.06.2009" Time="18:52:58" DateTime="2009-06-17T16:52:58.468Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">the location where the connection string and other app settings are stored</Text></Message><Message Date="17.06.2009" Time="18:53:16" DateTime="2009-06-17T16:53:16.671Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">u can also change the connection string in there to match where the project is on ur pc</Text></Message><Message Date="17.06.2009" Time="18:53:44" DateTime="2009-06-17T16:53:44.953Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">The reason that i utilized LINQ is because it's efficient, faster, and keeps it object oriented</Text></Message><Message Date="17.06.2009" Time="18:53:51" DateTime="2009-06-17T16:53:51.343Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">so it's easy pulling back data</Text></Message><Message Date="17.06.2009" Time="18:53:56" DateTime="2009-06-17T16:53:56.359Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">and submitting it.</Text></Message><Message Date="17.06.2009" Time="18:54:14" DateTime="2009-06-17T16:54:14.671Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">if you do it via queries and try it that way, it'd be a bitch to get done anytime soon</Text></Message><Message Date="17.06.2009" Time="18:56:54" DateTime="2009-06-17T16:56:54.140Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Alright, I'll try to play around with it :) I may send you a couple of questions :P</Text></Message><Message Date="17.06.2009" Time="18:57:10" DateTime="2009-06-17T16:57:10.078Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I've transfered the last 40$, thanks for your help :)</Text></Message><Message Date="17.06.2009" Time="18:57:33" DateTime="2009-06-17T16:57:33.453Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">No problem, msn is the best place to reach me if anything, I am always online while at work</Text></Message><Message Date="17.06.2009" Time="18:57:46" DateTime="2009-06-17T16:57:46.093Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">so don't bother e-mailing me during my work hours</Text></Message><Message Date="17.06.2009" Time="18:57:53" DateTime="2009-06-17T16:57:53.859Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">11am - 7pm pacific usa time</Text></Message><Message Date="17.06.2009" Time="18:57:58" DateTime="2009-06-17T16:57:58.390Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Alright ;)</Text></Message><Message Date="17.06.2009" Time="18:58:13" DateTime="2009-06-17T16:58:13.156Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">Here is a good challenge</Text></Message><Message Date="17.06.2009" Time="18:58:23" DateTime="2009-06-17T16:58:23.562Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">see the Checkbox for Active</Text></Message><Message Date="17.06.2009" Time="18:58:33" DateTime="2009-06-17T16:58:33.109Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">in the front-gui</Text></Message><Message Date="17.06.2009" Time="18:58:52" DateTime="2009-06-17T16:58:52.296Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">see if u can hook it up so when u click on it, it places the invert value to the current record</Text></Message><Message Date="17.06.2009" Time="18:59:35" DateTime="2009-06-17T16:59:35.765Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the invert value? do you mean the switch between 0/1?</Text></Message><Message Date="17.06.2009" Time="19:00:12" DateTime="2009-06-17T17:00:12.546Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">yes</Text></Message><Message Date="17.06.2009" Time="19:01:19" DateTime="2009-06-17T17:01:19.546Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Alright, I may have a challenge there :) I'll try to see if I can get it to work, will probably mess up your coding standards though :P</Text></Message><Message Date="17.06.2009" Time="19:01:30" DateTime="2009-06-17T17:01:30.390Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">lol :P</Text></Message><Message Date="17.06.2009" Time="19:02:11" DateTime="2009-06-17T17:02:11.843Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">here is a hint</Text></Message><Invitation Date="17.06.2009" Time="19:02:38" DateTime="2009-06-17T17:02:38.500Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><File>2.png</File><Text Style="color:#545454; ">+TuAnKoI sender 2.png</Text></Invitation><InvitationResponse Date="17.06.2009" Time="19:03:04" DateTime="2009-06-17T17:03:04.968Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><File>D:\Privat\Dokumenter\Mine mottatte filer\2.png</File><Text Style="color:#545454; ">Du har mottatt D:\Privat\Dokumenter\Mine mottatte filer\2.png fra +TuAnKoI.</Text></InvitationResponse><Message Date="17.06.2009" Time="19:03:13" DateTime="2009-06-17T17:03:13.921Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">hook that event in the Form1_Load function</Text></Message><Message Date="17.06.2009" Time="19:03:59" DateTime="2009-06-17T17:03:59.781Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks for the challenge, and the hint :) I'll definately brag about it to you if I get it to work ;)</Text></Message><Message Date="17.06.2009" Time="19:04:06" DateTime="2009-06-17T17:04:06.953Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">hehe :D</Text></Message><Message Date="17.06.2009" Time="19:04:19" DateTime="2009-06-17T17:04:19.765Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">u have enough clues in the source to figure out how to get the currently selected row</Text></Message><Message Date="17.06.2009" Time="19:04:32" DateTime="2009-06-17T17:04:32.234Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">and value of a specific cell so it'll be a breeze once u understand that</Text></Message><Message Date="17.06.2009" Time="19:05:12" DateTime="2009-06-17T17:05:12.859Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">we'll see ;)</Text></Message><Message Date="17.06.2009" Time="19:06:23" DateTime="2009-06-17T17:06:23.328Z" SessionID="3"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; font-weight:bold; color:#000000; ">K bro, take it easy i'm off</Text></Message><Message Date="25.06.2009" Time="23:47:18" DateTime="2009-06-25T21:47:18.859Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey</Text></Message><Message Date="25.06.2009" Time="23:47:24" DateTime="2009-06-25T21:47:24.546Z" SessionID="4"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">Sup</Text></Message><Message Date="25.06.2009" Time="23:47:36" DateTime="2009-06-25T21:47:36.656Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nothing</Text></Message><Message Date="25.06.2009" Time="23:47:38" DateTime="2009-06-25T21:47:38.562Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you?</Text></Message><Message Date="25.06.2009" Time="23:47:53" DateTime="2009-06-25T21:47:53.781Z" SessionID="4"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">Just chillin</Text></Message><Message Date="25.06.2009" Time="23:48:21" DateTime="2009-06-25T21:48:21.984Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool, did you understand my mail last week?</Text></Message><Message Date="25.06.2009" Time="23:48:59" DateTime="2009-06-25T21:48:59.656Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i wasn't really sure what to ask for, but you said you used a optimizer</Text></Message><Message Date="25.06.2009" Time="23:49:17" DateTime="2009-06-25T21:49:17.328Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">having a hard time grasping the code :P</Text></Message><Message Date="25.06.2009" Time="23:53:38" DateTime="2009-06-25T21:53:38.687Z" SessionID="4"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">You are? It's pretty straight forward</Text></Message><Message Date="25.06.2009" Time="23:53:39" DateTime="2009-06-25T21:53:39.343Z" SessionID="4"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">=]</Text></Message><Message Date="25.06.2009" Time="23:53:58" DateTime="2009-06-25T21:53:58.703Z" SessionID="4"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">I will look back at the e-mail, I honestly have been getting home and falling asleep, so I am not up to speed these days</Text></Message><Message Date="25.06.2009" Time="23:54:42" DateTime="2009-06-25T21:54:42.500Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">heh, me neither.. 16/17 hours of work every day this week</Text></Message><Message Date="25.06.2009" Time="23:57:16" DateTime="2009-06-25T21:57:16.187Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">uhm, did you send me the project on msn or mail? can't find it</Text></Message><Message Date="25.06.2009" Time="23:57:34" DateTime="2009-06-25T21:57:34.578Z" SessionID="4"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">i think msn</Text></Message><Message Date="25.06.2009" Time="23:58:33" DateTime="2009-06-25T21:58:33.875Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah, found it</Text></Message><Message Date="25.06.2009" Time="23:59:51" DateTime="2009-06-25T21:59:51.937Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="+TuAnKoI"/></To><Text Style="font-family:Segoe UI; color:#000000; ">lets say i e.g. want to change which columns i want to use, can't find which section these are defined</Text></Message><Message Date="26.06.2009" Time="00:22:13" DateTime="2009-06-25T22:22:13.625Z" SessionID="4"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">Ah I see</Text></Message><Message Date="26.06.2009" Time="00:22:16" DateTime="2009-06-25T22:22:16.953Z" SessionID="4"><From><User FriendlyName="+TuAnKoI"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">I will assist later on</Text></Message></Log>

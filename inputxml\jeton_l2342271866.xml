<?xml version="1.0"?>
<?xml-stylesheet type='text/xsl' href='MessageLog.xsl'?>
<Log FirstSessionID="1" LastSessionID="2"><Message Date="22.10.2011" Time="14:26:08" DateTime="2011-10-22T12:26:08.111Z" SessionID="1"><From><User FriendlyName="jeton (L) leonora"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hi jorn</Text></Message><Message Date="22.10.2011" Time="14:27:36" DateTime="2011-10-22T12:27:36.979Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="jeton"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, how's the rig going?</Text></Message><Message Date="22.10.2011" Time="14:27:58" DateTime="2011-10-22T12:27:58.040Z" SessionID="1"><From><User FriendlyName="jeton"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so far so good </Text></Message><Message Date="22.10.2011" Time="14:29:56" DateTime="2011-10-22T12:29:56.195Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="jeton"/></To><Text Style="font-family:Segoe UI; color:#000000; ">good stuff</Text></Message><Message Date="22.10.2011" Time="14:31:15" DateTime="2011-10-22T12:31:15.759Z" SessionID="1"><From><User FriendlyName="jeton"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeap , I was wondering if we could have a small video for skining ! it will be cool </Text></Message><Message Date="22.10.2011" Time="14:35:07" DateTime="2011-10-22T12:35:07.395Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="jeton"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you should check out youtube, there's a ton of skinning tutorials on there
http://www.youtube.com/watch?v=xMuo8SsIaQA
http://www.youtube.com/watch?v=dD5adx9SdH0&amp;feature=related</Text></Message><Message Date="22.10.2011" Time="14:40:15" DateTime="2011-10-22T12:40:15.689Z" SessionID="1"><From><User FriendlyName="jeton"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeap , </Text></Message><Message Date="22.10.2011" Time="14:40:30" DateTime="2011-10-22T12:40:30.248Z" SessionID="1"><From><User FriendlyName="jeton"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">if you dont mind , </Text></Message><Message Date="22.10.2011" Time="14:41:16" DateTime="2011-10-22T12:41:16.293Z" SessionID="1"><From><User FriendlyName="jeton"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I am working in fk , and I put a Nurbs Cricle Ctrl  </Text></Message><Message Date="22.10.2011" Time="14:42:17" DateTime="2011-10-22T12:42:17.738Z" SessionID="1"><From><User FriendlyName="jeton"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but when I want to rotate , when I select the vertex in one side is selecting also other side , kind of mirorr </Text></Message><Message Date="22.10.2011" Time="14:47:16" DateTime="2011-10-22T12:47:16.642Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="jeton"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, you should use the command i used in the arm video:
parent -s -r circleShape jointName</Text></Message><Message Date="22.10.2011" Time="14:47:27" DateTime="2011-10-22T12:47:27.347Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="jeton"/></To><Text Style="font-family:Segoe UI; color:#000000; ">then it will inherit the local rotation axis from the joints</Text></Message><Message Date="22.10.2011" Time="14:54:09" DateTime="2011-10-22T12:54:09.565Z" SessionID="1"><From><User FriendlyName="jeton"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks , </Text></Message><Message Date="29.01.2012" Time="18:13:36" DateTime="2012-01-29T17:13:36.584Z" SessionID="2"><From><User FriendlyName="jeton &amp; leonora"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hi jorn </Text></Message></Log>

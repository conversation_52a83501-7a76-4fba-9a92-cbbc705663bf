<?xml version="1.0"?>
<?xml-stylesheet type='text/xsl' href='MessageLog.xsl'?>
<Log FirstSessionID="1" LastSessionID="9"><Message Date="20.09.2011" Time="21:15:49" DateTime="2011-09-20T19:15:49.418Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">halla</Text></Message><Message Date="20.09.2011" Time="21:16:01" DateTime="2011-09-20T19:16:01.351Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">halla pølsa :)</Text></Message><Message Date="20.09.2011" Time="21:17:15" DateTime="2011-09-20T19:17:15.147Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe:) jeg sitter å prøver ut litt MEL;) skjønner ikke så altfor mye her må jeg si:P</Text></Message><Message Date="20.09.2011" Time="21:17:46" DateTime="2011-09-20T19:17:46.151Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe ;) anbefaller deg å starte med noe skikkelig basic, og da rett og slett se hva maya printer</Text></Message><Message Date="20.09.2011" Time="21:18:09" DateTime="2011-09-20T19:18:09.687Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">alt jeg skriver i mel gjør jeg manuelt først</Text></Message><Message Date="20.09.2011" Time="21:18:30" DateTime="2011-09-20T19:18:30.759Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">så ser jeg bare på kommandoene, og tilpasser det med variabler.. men de første scriptene jeg mekka hadde jeg ikke variabler i</Text></Message><Message Date="20.09.2011" Time="21:19:35" DateTime="2011-09-20T19:19:35.013Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ja det er det jeg gjør nå, det jeg ikke finner er kanskje litt dumt men:P jeg har laget et 3 sett med joints, også har jeg skrivd at circle skal point constraintes til den ene jointen, men jeg klarer ikke å slette den:P</Text></Message><Message Date="20.09.2011" Time="21:19:37" DateTime="2011-09-20T19:19:37.383Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">f.eks. å sette opp et bein og lage ikHandles</Text></Message><Message Date="20.09.2011" Time="21:19:48" DateTime="2011-09-20T19:19:48.967Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah :)</Text></Message><Message Date="20.09.2011" Time="21:20:33" DateTime="2011-09-20T19:20:33.415Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok, et smart triks som jeg ikke lærte meg før jeg gikk på animasjon;
delete `pointConstraint joint circle`;</Text></Message><Message Date="20.09.2011" Time="21:21:16" DateTime="2011-09-20T19:21:16.423Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">det den gjør da er at den sletter *resultatet* av pointConstrainen, men hver gang man bruker de fnuttene her: `` så må den faktisk kjøre kommandoen</Text></Message><Message Date="20.09.2011" Time="21:21:29" DateTime="2011-09-20T19:21:29.559Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">så det vil si at den faktisk kjører pointConstrainen, så sletter den den etterpå</Text></Message><Message Date="20.09.2011" Time="21:22:00" DateTime="2011-09-20T19:22:00.637Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">okey, men det er jo det jeg vil er det ikke det a?</Text></Message><Message Date="20.09.2011" Time="21:22:13" DateTime="2011-09-20T19:22:13.815Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">regner med det ;)</Text></Message><Message Date="20.09.2011" Time="21:23:03" DateTime="2011-09-20T19:23:03.635Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe:P men den funka greit:)</Text></Message><Message Date="20.09.2011" Time="21:24:07" DateTime="2011-09-20T19:24:07.591Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">jess, sånn jeg gjorde det der når jeg lærte meg mel, var at jeg skrev:
pointConstraint joint circle -n "tempConstraint";
delete "tempConstraint";</Text></Message><Message Date="20.09.2011" Time="21:24:18" DateTime="2011-09-20T19:24:18.679Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">det er ganske tungvint, men det funker</Text></Message><Message Date="20.09.2011" Time="21:24:37" DateTime="2011-09-20T19:24:37.687Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">altså at jeg gir et navn til constrainen idet jeg lager den, så sletter jeg den etterpå i og med at jeg visste navnet</Text></Message><Message Date="20.09.2011" Time="21:24:48" DateTime="2011-09-20T19:24:48.032Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe jeg kjører en ganske ille en tror jeg.</Text></Message><Message Date="20.09.2011" Time="21:25:00" DateTime="2011-09-20T19:25:00.404Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">select -r char_l_hip_jnt ;
select -tgl char_l_hip_ctrl ;
doCreatePointConstraintArgList 1 { "0","0","0","0","0","0","0","1","","1" };
delete -cn char_l_hip_ctrl_pointConstraint1;
:P</Text></Message><Message Date="20.09.2011" Time="21:25:15" DateTime="2011-09-20T19:25:15.763Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">kommer straks tilbake,</Text></Message><Message Date="20.09.2011" Time="21:25:45" DateTime="2011-09-20T19:25:45.927Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">akkurat sånn jeg starta også, bra det :D btw, når man lager en pointConstraint så kommer det opp to kommandoer, funker å kjøre den andre også</Text></Message><Message Date="20.09.2011" Time="21:26:20" DateTime="2011-09-20T19:26:20.471Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">den her: pointConstraint -offset 0 0 0 -weight 1;
</Text></Message><Message Date="20.09.2011" Time="21:26:35" DateTime="2011-09-20T19:26:35.575Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">for da kan man definere navnene på de man vil constraine i samme kommandoen</Text></Message><Message Date="20.09.2011" Time="21:26:51" DateTime="2011-09-20T19:26:51.352Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sånn her:
pointConstraint -offset 0 0 0 -weight 1 master slave;
</Text></Message><Message Date="20.09.2011" Time="21:27:10" DateTime="2011-09-20T19:27:10.663Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">master er det objektet jeg vil constraine til, og slave er det objektet som blir pointConstrainet</Text></Message><Message Date="20.09.2011" Time="21:28:20" DateTime="2011-09-20T19:28:20.606Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">må teste ut en god del her merker jeg, siden magne er så treg på de karakterene vi skal ha så tenkte jeg at jeg kanskje skulle lage et script;)</Text></Message><Message Date="20.09.2011" Time="21:29:09" DateTime="2011-09-20T19:29:09.223Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">høres lurt ut ;) utrolig mye man kan gjøre med det altså, og i startet er det litt stress, men når du først kommer igang så er det litt kulere, for da kan man faktisk lage ting som er nyttig</Text></Message><Message Date="20.09.2011" Time="21:29:27" DateTime="2011-09-20T19:29:27.319Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">pluss at det blir lettere og lettere jo mer man kan</Text></Message><Message Date="20.09.2011" Time="21:30:57" DateTime="2011-09-20T19:30:57.826Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ja jeg er nødt for å trene en del på detta, er litt kult at man bare trykker på en knapp så er det i orden.</Text></Message><Message Date="20.09.2011" Time="21:31:15" DateTime="2011-09-20T19:31:15.831Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i know, magisk ;)</Text></Message><Message Date="20.09.2011" Time="21:33:10" DateTime="2011-09-20T19:33:10.340Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">rett å slett:) men den kommandoen du ga meg "pointConstraint -offset 0 0 0 -weight 1;" hva betyr det? må jeg skrive inn noe mere? kan kanskje ta det i morgen, det er kanskje ikke så lett å ta det over Msn:P</Text></Message><Message Date="20.09.2011" Time="21:33:58" DateTime="2011-09-20T19:33:58.887Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">"-offset 0 0 0 -weight 1" kan egentlig fjernes, det er bare at man kan liksom definere flere ting når man lager pointConstrainen</Text></Message><Message Date="20.09.2011" Time="21:34:31" DateTime="2011-09-20T19:34:31.751Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">egentlig så holder det å skrive:
pointConstraint navn1 navn2</Text></Message><Message Date="20.09.2011" Time="21:35:01" DateTime="2011-09-20T19:35:01.960Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">må løpe å bistå frøkna litt her, så forsvinner litt</Text></Message><Message Date="20.09.2011" Time="21:35:31" DateTime="2011-09-20T19:35:31.200Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">den er go. jeg må stikke nå selv, skal se på big brother:P</Text></Message><Message Date="20.09.2011" Time="21:35:32" DateTime="2011-09-20T19:35:32.343Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">men er litt lettere å kikke på når du er fått litt søvn ( om det blir noe av det da :P)</Text></Message><Message Date="20.09.2011" Time="21:35:42" DateTime="2011-09-20T19:35:42.391Z" SessionID="1"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">jess, snæx ;)</Text></Message><Message Date="20.09.2011" Time="21:35:50" DateTime="2011-09-20T19:35:50.268Z" SessionID="1"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">det gjør vi:)</Text></Message><Message Date="28.09.2011" Time="18:41:44" DateTime="2011-09-28T16:41:44.534Z" SessionID="2"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">halla:)</Text></Message><Message Date="28.09.2011" Time="18:43:16" DateTime="2011-09-28T16:43:16.764Z" SessionID="2"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">halla mister :)</Text></Message><Message Date="28.09.2011" Time="18:45:38" DateTime="2011-09-28T16:45:38.024Z" SessionID="2"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">veit du om noen bra gratis karakterer med rigg?</Text></Message><Message Date="28.09.2011" Time="18:45:57" DateTime="2011-09-28T16:45:57.020Z" SessionID="2"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hmm, skal sjekke</Text></Message><Message Date="28.09.2011" Time="18:48:02" DateTime="2011-09-28T16:48:02.076Z" SessionID="2"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh fant en som skal være veldig bra</Text></Message><Message Date="28.09.2011" Time="18:48:08" DateTime="2011-09-28T16:48:08.428Z" SessionID="2"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">har ikke testet den selv, men sender den over</Text></Message><Message Date="28.09.2011" Time="18:48:19" DateTime="2011-09-28T16:48:19.532Z" SessionID="2"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ser clean og fin ut, og har sett den i flere reels</Text></Message><Message Date="28.09.2011" Time="18:48:31" DateTime="2011-09-28T16:48:31.039Z" SessionID="2"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">kult:)</Text></Message><Invitation Date="28.09.2011" Time="18:49:00" DateTime="2011-09-28T16:49:00.043Z" SessionID="2"><From><User FriendlyName="Jørn"/></From><File>C:\Users\<USER>\Desktop\blake_v1.6.zip</File><Text Style="color:#545454; ">Jørn sender C:\Users\<USER>\Desktop\blake_v1.6.zip</Text></Invitation><Message Date="28.09.2011" Time="18:49:28" DateTime="2011-09-28T16:49:28.860Z" SessionID="2"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nettet her er helt på trynet, så jeg veit ikke helt hvor fort detta går:P</Text></Message><Message Date="28.09.2011" Time="18:49:49" DateTime="2011-09-28T16:49:49.515Z" SessionID="2"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">brrb</Text></Message><InvitationResponse Date="28.09.2011" Time="18:51:01" DateTime="2011-09-28T16:51:01.923Z" SessionID="2"><From><User FriendlyName="Marius"/></From><File>C:\Users\<USER>\Desktop\blake_v1.6.zip</File><Text Style="color:#545454; ">Overføring av "blake_v1.6.zip" er fullført.</Text></InvitationResponse><Message Date="28.09.2011" Time="19:01:19" DateTime="2011-09-28T17:01:19.372Z" SessionID="2"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">den var kul!:) kan bli fett å animere han:) nå er taco'n her ferdig;) så jeg blir borte en liten stund.</Text></Message><Message Date="13.10.2011" Time="23:31:35" DateTime="2011-10-13T21:31:35.899Z" SessionID="3"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">halla.</Text></Message><Message Date="23.10.2011" Time="14:34:27" DateTime="2011-10-23T12:34:27.481Z" SessionID="4"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">her står det litt om UI layouts i mel :)
http://wiki.bk.tudelft.nl/toi-pedia/MEL_UI_Layouts</Text></Message><Message Date="20.12.2011" Time="23:51:36" DateTime="2011-12-20T22:51:36.809Z" SessionID="5"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">er du der?</Text></Message><Message Date="20.12.2011" Time="23:52:04" DateTime="2011-12-20T22:52:04.124Z" SessionID="5"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">halla, jepp :)</Text></Message><Message Date="20.12.2011" Time="23:52:42" DateTime="2011-12-20T22:52:42.018Z" SessionID="5"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">halla:) sitter med et script til Odd her, også husker jeg ikke hvordan jeg skrev en prosedyre til UI vindue</Text></Message><Message Date="20.12.2011" Time="23:53:22" DateTime="2011-12-20T22:53:22.684Z" SessionID="5"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">du får til å lage vindu uten procedure eller?</Text></Message><Message Date="20.12.2011" Time="23:54:06" DateTime="2011-12-20T22:54:06.054Z" SessionID="5"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ja men jeg trenger det til knappene.</Text></Message><Message Date="20.12.2011" Time="23:54:22" DateTime="2011-12-20T22:54:22.526Z" SessionID="5"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">er det ikke bare "proc navnet()"</Text></Message><Message Date="20.12.2011" Time="23:55:04" DateTime="2011-12-20T22:55:04.044Z" SessionID="5"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">skjønner, når du skal lage prosedyrer så er det sånn her:
global proc prosedyreNavn()
{
   her er kommandoene;
}</Text></Message><Message Date="20.12.2011" Time="23:55:18" DateTime="2011-12-20T22:55:18.300Z" SessionID="5"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">så alt som er inni brakkettene vil kjøres når du kjører prosedyren :)</Text></Message><Message Date="20.12.2011" Time="23:55:20" DateTime="2011-12-20T22:55:20.049Z" SessionID="5"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">aha;)</Text></Message><Message Date="20.12.2011" Time="23:55:51" DateTime="2011-12-20T22:55:51.951Z" SessionID="5"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">bak knappen skal jeg skrive "command "pros.navnet";"</Text></Message><Message Date="20.12.2011" Time="23:56:10" DateTime="2011-12-20T22:56:10.972Z" SessionID="5"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">helt riktig :) </Text></Message><Message Date="20.12.2011" Time="23:56:42" DateTime="2011-12-20T22:56:42.908Z" SessionID="5"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">må bare huske bindestreken på commanden</Text></Message><Message Date="20.12.2011" Time="23:56:53" DateTime="2011-12-20T22:56:53.228Z" SessionID="5"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">button -command prosedyreNavn</Text></Message><Message Date="20.12.2011" Time="23:57:16" DateTime="2011-12-20T22:57:16.817Z" SessionID="5"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">stemmer det</Text></Message><Message Date="20.12.2011" Time="23:58:35" DateTime="2011-12-20T22:58:35.706Z" SessionID="5"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">da er blei jeg ferdig:)</Text></Message><Message Date="20.12.2011" Time="23:58:48" DateTime="2011-12-20T22:58:48.636Z" SessionID="5"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sweet da :) slags script er det du driver med?</Text></Message><Message Date="20.12.2011" Time="23:59:32" DateTime="2011-12-20T22:59:32.512Z" SessionID="5"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Odd ville ha et script det er arealight skulle midt stilles inn i et vindu med retninger osv</Text></Message><Message Date="20.12.2011" Time="23:59:46" DateTime="2011-12-20T22:59:46.475Z" SessionID="5"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">men nå våkna gutt ungen. så vi får snakkes:)</Text></Message><Message Date="21.12.2011" Time="00:00:05" DateTime="2011-12-20T23:00:05.180Z" SessionID="5"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok, snakkes ;)</Text></Message><Message Date="22.02.2012" Time="00:48:09" DateTime="2012-02-21T23:48:09.997Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">halla:)</Text></Message><Message Date="22.02.2012" Time="00:48:33" DateTime="2012-02-21T23:48:33.487Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hei du :)</Text></Message><Message Date="22.02.2012" Time="00:48:42" DateTime="2012-02-21T23:48:42.318Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">har ikke du lagt deg enda? :-O</Text></Message><Message Date="22.02.2012" Time="00:49:46" DateTime="2012-02-21T23:49:46.430Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nei nå har jeg rigga om pc'n på spisestue bordet:P prøver å jobbe litt med oppgaven. det ser ikke ut som det funker helt så:S</Text></Message><Message Date="22.02.2012" Time="00:50:11" DateTime="2012-02-21T23:50:11.662Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe :P</Text></Message><Message Date="22.02.2012" Time="00:50:17" DateTime="2012-02-21T23:50:17.919Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hva er det som ikke funker?</Text></Message><Message Date="22.02.2012" Time="00:50:44" DateTime="2012-02-21T23:50:44.581Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">jeg får ikke åpnet opp noen maya filer..  </Text></Message><Message Date="22.02.2012" Time="00:50:47" DateTime="2012-02-21T23:50:47.559Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Error: line 1: Unknown Maya file version: 2012.</Text></Message><Message Date="22.02.2012" Time="00:51:06" DateTime="2012-02-21T23:51:06.335Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">har du eldre versjon hjemme da eller?</Text></Message><Message Date="22.02.2012" Time="00:51:35" DateTime="2012-02-21T23:51:35.634Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ja. kan jeg ikke åpne opp 2012 med 2011?:S</Text></Message><Message Date="22.02.2012" Time="00:51:57" DateTime="2012-02-21T23:51:57.582Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">joa, bare gå inn på option boxen når du går på "open" i maya</Text></Message><Message Date="22.02.2012" Time="00:52:04" DateTime="2012-02-21T23:52:04.511Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">så slår du på ignore version</Text></Message><Message Date="22.02.2012" Time="00:52:57" DateTime="2012-02-21T23:52:57.524Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">såpass ja:S nå funka det:P</Text></Message><Message Date="22.02.2012" Time="00:53:08" DateTime="2012-02-21T23:53:08.287Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">jess ;)</Text></Message><Message Date="22.02.2012" Time="00:53:55" DateTime="2012-02-21T23:53:55.473Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">takk:)</Text></Message><Message Date="22.02.2012" Time="00:54:17" DateTime="2012-02-21T23:54:17.502Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">bare hyggelig :)</Text></Message><Message Date="22.02.2012" Time="00:54:34" DateTime="2012-02-21T23:54:34.808Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">leste litt om det på nettet og dem går inn i scripte på fila og gjør om 2012 til 2011, hørtes tungvindt ut;)</Text></Message><Message Date="22.02.2012" Time="00:54:52" DateTime="2012-02-21T23:54:52.622Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">jepp :P</Text></Message><Message Date="22.02.2012" Time="00:55:22" DateTime="2012-02-21T23:55:22.392Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">du driver med så sent da?</Text></Message><Message Date="22.02.2012" Time="00:55:44" DateTime="2012-02-21T23:55:44.510Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">driver med den robot-katt-riggen</Text></Message><Message Date="22.02.2012" Time="00:55:58" DateTime="2012-02-21T23:55:58.645Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">den hi tech modellen?</Text></Message><Message Date="22.02.2012" Time="00:56:13" DateTime="2012-02-21T23:56:13.999Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ja, hver gang jeg kommer et steg videre, så går det opp for meg hvor avansert den egentlig er</Text></Message><Message Date="22.02.2012" Time="00:56:31" DateTime="2012-02-21T23:56:31.583Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">men er nesten i mål med det vanskeligste nå, bakbeinet.. har det snart perfekt</Text></Message><Message Date="22.02.2012" Time="00:56:38" DateTime="2012-02-21T23:56:38.975Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">er bare en siste utfordring igjen</Text></Message><Message Date="22.02.2012" Time="00:58:29" DateTime="2012-02-21T23:58:29.670Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice. digg og bli ferdig med da? har du noen planer om animasjon da?</Text></Message><Message Date="22.02.2012" Time="00:59:09" DateTime="2012-02-21T23:59:09.951Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">jah, gleder meg sykt til å bli ferdig med denne... med en ordentlig presentasjon av riggen så er det en mer enn bra nok rig-reel tror jeg </Text></Message><Message Date="22.02.2012" Time="00:59:15" DateTime="2012-02-21T23:59:15.295Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">har ikke tenkt så mye på animasjon enda</Text></Message><Message Date="22.02.2012" Time="01:00:18" DateTime="2012-02-22T00:00:18.772Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">fett. men du skal animere?</Text></Message><Message Date="22.02.2012" Time="01:00:39" DateTime="2012-02-22T00:00:39.550Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">seff, når jeg omsider kommer så langt</Text></Message><Message Date="22.02.2012" Time="01:01:38" DateTime="2012-02-22T00:01:38.178Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe ja ta en ting om gangen kanskje:P men detta funka dårlig du, maya krasjer vær gang jeg prøver og åpne opp animasjonen min:S</Text></Message><Message Date="22.02.2012" Time="01:02:02" DateTime="2012-02-22T00:02:02.623Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hmm, da tror jeg jeg ville fått lasta inn maya 2012</Text></Message><Message Date="22.02.2012" Time="01:02:17" DateTime="2012-02-22T00:02:17.295Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">gå på cgpeers.com, da skal det ikke være så vanskelig å finne</Text></Message><Message Date="22.02.2012" Time="01:03:00" DateTime="2012-02-22T00:03:00.630Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">er ikke medlem:P</Text></Message><Message Date="22.02.2012" Time="01:03:29" DateTime="2012-02-22T00:03:29.103Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">er bare å registrere det</Text></Message><Message Date="22.02.2012" Time="01:04:01" DateTime="2012-02-22T00:04:01.363Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nja, er det mye nyttig der?</Text></Message><Message Date="22.02.2012" Time="01:04:08" DateTime="2012-02-22T00:04:08.654Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">maya 2012 f.eks</Text></Message><Message Date="22.02.2012" Time="01:04:12" DateTime="2012-02-22T00:04:12.367Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">;)</Text></Message><Message Date="22.02.2012" Time="01:04:32" DateTime="2012-02-22T00:04:32.053Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe:P</Text></Message><Message Date="22.02.2012" Time="01:05:49" DateTime="2012-02-22T00:05:49.689Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">kommet noe fremover på "bry deg" filmen da?</Text></Message><Message Date="22.02.2012" Time="01:09:55" DateTime="2012-02-22T00:09:55.023Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">tja jo, jonas jobber med animatic, så kikke på den på fredag :)</Text></Message><Message Date="22.02.2012" Time="01:10:38" DateTime="2012-02-22T00:10:38.511Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">men lurer på om jeg skal ta å køye nå, få inn maya 2012 så ordner det seg ;)</Text></Message><Message Date="22.02.2012" Time="01:10:43" DateTime="2012-02-22T00:10:43.119Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">så prates vi</Text></Message><Message Date="22.02.2012" Time="01:11:05" DateTime="2012-02-22T00:11:05.204Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ja det burde jeg også:P vi prates:)</Text></Message><Message Date="22.02.2012" Time="01:11:06" DateTime="2012-02-22T00:11:06.383Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">tror jeg stikker innom skolen på fredag en tur om det er noe :)</Text></Message><Message Date="22.02.2012" Time="01:11:13" DateTime="2012-02-22T00:11:13.311Z" SessionID="6"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">jess, snakkes :)</Text></Message><Message Date="22.02.2012" Time="01:11:29" DateTime="2012-02-22T00:11:29.726Z" SessionID="6"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">den er grei:)</Text></Message><Message Date="26.02.2012" Time="19:17:44" DateTime="2012-02-26T18:17:44.296Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">halla..</Text></Message><Message Date="26.02.2012" Time="19:17:54" DateTime="2012-02-26T18:17:54.437Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hei du</Text></Message><Message Date="26.02.2012" Time="19:21:04" DateTime="2012-02-26T18:21:04.167Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">kan jeg constraine i referance fila? eller må jeg gjøre det i rig fila? jeg skal ha en "parent constraint" sånn at jeg den ene hånda kontrolerer den andre, grunner er får og holde sverdet i begge hender. hvis du har annet å gjøre kan vi ta det i moergen</Text></Message><Message Date="26.02.2012" Time="19:22:20" DateTime="2012-02-26T18:22:20.038Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">det skal gå bra å gjøre det i den refererte fila, men om det er noe du skal bruke i mange shots så kan det være lurt å gjøre det i rig-fila</Text></Message><Message Date="26.02.2012" Time="20:42:51" DateTime="2012-02-26T19:42:51.284Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oki, takk</Text></Message><Message Date="26.02.2012" Time="21:31:52" DateTime="2012-02-26T20:31:52.310Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">kommet langt med filmen din ell?:)</Text></Message><Message Date="26.02.2012" Time="21:33:20" DateTime="2012-02-26T20:33:20.950Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">den bry deg tingen som vi holder på med på 3d-design du tenker på? </Text></Message><Message Date="26.02.2012" Time="21:34:07" DateTime="2012-02-26T20:34:07.234Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">er det 3d design som skal lage den? men ja det var den jeg tenkte på.</Text></Message><Message Date="26.02.2012" Time="21:35:08" DateTime="2012-02-26T20:35:08.165Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">jess, er prosjekt som de hovedsaklig skal lage, jeg må bare pitche inn for å få det ferdig :)</Text></Message><Message Date="26.02.2012" Time="21:35:25" DateTime="2012-02-26T20:35:25.749Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">animaticen er klar, så nå er det produksjon igjen ;)</Text></Message><Message Date="26.02.2012" Time="21:36:36" DateTime="2012-02-26T20:36:36.674Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">aha, jeg trodde du hadde fått et oppdrag av noen jeg:P </Text></Message><Message Date="26.02.2012" Time="21:37:11" DateTime="2012-02-26T20:37:11.285Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">neij :P</Text></Message><Message Date="26.02.2012" Time="21:37:28" DateTime="2012-02-26T20:37:28.773Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">har faktisk ikke hatt et eneste freelance oppdrag innen 3d :P</Text></Message><Message Date="26.02.2012" Time="21:38:42" DateTime="2012-02-26T20:38:42.878Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">men er det sånn at du kan ta det i mot og jobbe med det etter jobb tid? har man tid til det?:S</Text></Message><Message Date="26.02.2012" Time="21:39:28" DateTime="2012-02-26T20:39:28.949Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nei det er det, man må isåfall gjøre det sånn</Text></Message><Message Date="26.02.2012" Time="21:39:39" DateTime="2012-02-26T20:39:39.206Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">er isåfall stress :P</Text></Message><Message Date="26.02.2012" Time="21:41:01" DateTime="2012-02-26T20:41:01.816Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">okey. nei faen jeg har liggi våken i hele natt, har ikke tenkt på annet en jobb. jeg kan ikke jobbe med freelance, det tør jeg ikke. kanskje når dama begynner å jobbe.</Text></Message><Message Date="26.02.2012" Time="21:42:22" DateTime="2012-02-26T20:42:22.037Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">faen jeg ser den, vanskelig å slappe av.. men ja, på freelance så er det lurt å ta på seg jobber man føler seg trygg på</Text></Message><Message Date="26.02.2012" Time="21:42:46" DateTime="2012-02-26T20:42:46.455Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">det som er litt stress med animasjon og sånn, for det som er bra nok for en fyr er ikke nødvendigvis bra nok for en annen fyr</Text></Message><Message Date="26.02.2012" Time="21:42:58" DateTime="2012-02-26T20:42:58.322Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">pluss at det er stress å pris-sette seg</Text></Message><Message Date="26.02.2012" Time="21:43:16" DateTime="2012-02-26T20:43:16.974Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">og stress å finne kunder :P</Text></Message><Message Date="26.02.2012" Time="21:46:44" DateTime="2012-02-26T20:46:44.040Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ja jeg skulle gjort mye mere research ass:S</Text></Message><Message Date="26.02.2012" Time="21:48:17" DateTime="2012-02-26T20:48:17.893Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">åssen da?</Text></Message><Message Date="26.02.2012" Time="21:49:28" DateTime="2012-02-26T20:49:28.420Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">før jeg starta på skolen:P</Text></Message><Message Date="26.02.2012" Time="21:49:36" DateTime="2012-02-26T20:49:36.639Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">og familie</Text></Message><Message Date="26.02.2012" Time="21:52:00" DateTime="2012-02-26T20:52:00.678Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">men man kan liksom ikke sitte på gjerdet heller, må hoppe uti det uten å vite noen ganger.. men skjønner hva du mener sånn med tanke på at det er et usikkert yrke</Text></Message><Message Date="26.02.2012" Time="21:52:25" DateTime="2012-02-26T20:52:25.957Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">men om man står på og er flink til å selge seg selv så kommer det noe godt ut av det</Text></Message><Message Date="26.02.2012" Time="21:52:43" DateTime="2012-02-26T20:52:43.653Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">webside og showreel, pluss bra arbeider - da er du set</Text></Message><Message Date="26.02.2012" Time="21:53:16" DateTime="2012-02-26T20:53:16.325Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">men sånn med tanke på at du må ta ansvar for junior så er det endel vanskeligere for deg enn for de aller fleste</Text></Message><Message Date="26.02.2012" Time="21:54:12" DateTime="2012-02-26T20:54:12.141Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ja jeg er veldig mål bevist. og jeg har bestemt meg for og jobbe med animasjon. får jeg det ikke til sommeren så skal jeg få det til senere;)</Text></Message><Message Date="26.02.2012" Time="21:54:33" DateTime="2012-02-26T20:54:33.798Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">det er akkurat den innstillingen du må ha :)</Text></Message><Message Date="26.02.2012" Time="21:54:52" DateTime="2012-02-26T20:54:52.038Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">bare tenk at den tiden det tar før du får deg jobb på du bruke på å bli bedre</Text></Message><Message Date="26.02.2012" Time="21:55:36" DateTime="2012-02-26T20:55:36.311Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ja jeg skal jobbe mye med animasjonen hvertfall, koble av litt med rigging osv innimellom.</Text></Message><Message Date="26.02.2012" Time="21:58:15" DateTime="2012-02-26T20:58:15.493Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">jepp, så må man bare finne seg små klipp man vil animere som er motiverende å jobbe med</Text></Message><Message Date="26.02.2012" Time="21:58:19" DateTime="2012-02-26T20:58:19.814Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">og utfordrende</Text></Message><Message Date="26.02.2012" Time="21:58:22" DateTime="2012-02-26T20:58:22.758Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ikke for lange</Text></Message><Message Date="26.02.2012" Time="22:00:20" DateTime="2012-02-26T21:00:20.833Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ja nå har jeg en bra walk som jeg skal ha i reel'n min hvertfall.. jeg blei veldig fornøyd med det første shotet i filmen:)</Text></Message><Message Date="26.02.2012" Time="22:00:40" DateTime="2012-02-26T21:00:40.278Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">konge :D spent på å se :)</Text></Message><Message Date="26.02.2012" Time="22:00:54" DateTime="2012-02-26T21:00:54.693Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nå kommer jeg inn på animasjon igjen på mandag eller tirsdag ;)</Text></Message><Message Date="26.02.2012" Time="22:03:16" DateTime="2012-02-26T21:03:16.698Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice!:) </Text></Message><Message Date="26.02.2012" Time="22:05:14" DateTime="2012-02-26T21:05:14.984Z" SessionID="7"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nå er det tid film, snakkes i morra:)</Text></Message><Message Date="26.02.2012" Time="22:09:22" DateTime="2012-02-26T21:09:22.325Z" SessionID="7"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">jess, snakkes :)</Text></Message><Message Date="28.02.2012" Time="20:41:35" DateTime="2012-02-28T19:41:35.638Z" SessionID="8"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">halla. er di å</Text></Message><Message Date="28.02.2012" Time="20:41:42" DateTime="2012-02-28T19:41:42.359Z" SessionID="8"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">er du på skolen?</Text></Message><Message Date="28.02.2012" Time="20:42:06" DateTime="2012-02-28T19:42:06.291Z" SessionID="8"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">heidu, nei er hjemme</Text></Message><Message Date="28.02.2012" Time="20:42:13" DateTime="2012-02-28T19:42:13.988Z" SessionID="8"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hvordan det?</Text></Message><Message Date="28.02.2012" Time="20:44:14" DateTime="2012-02-28T19:44:14.792Z" SessionID="8"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">åja, nei jeg har glemt igjen lommeboka på skolen:P skulle ha vært i butikken og kjøpt middag:P hehe</Text></Message><Message Date="28.02.2012" Time="20:45:12" DateTime="2012-02-28T19:45:12.900Z" SessionID="8"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">skjønner, den er kjip :/</Text></Message><Message Date="29.04.2012" Time="20:26:53" DateTime="2012-04-29T18:26:53.255Z" SessionID="9"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">halla, er du der?</Text></Message><Message Date="29.04.2012" Time="21:10:02" DateTime="2012-04-29T19:10:02.563Z" SessionID="9"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Marius"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hei du, er her litt nu </Text></Message><Message Date="29.04.2012" Time="23:11:54" DateTime="2012-04-29T21:11:54.161Z" SessionID="9"><From><User FriendlyName="Marius"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nå er jeg her:P</Text></Message></Log>

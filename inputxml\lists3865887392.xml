<?xml version="1.0"?>
<?xml-stylesheet type='text/xsl' href='MessageLog.xsl'?>
<Log FirstSessionID="1" LastSessionID="1"><Message Date="27.07.2008" Time="21:51:01" DateTime="2008-07-27T19:51:01.062Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">Demo is ready: http://test.castwide.com/pdf/</Text></Message><Message Date="27.07.2008" Time="21:51:50" DateTime="2008-07-27T19:51:50.875Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">I just made a dummy database with 3 entries in it to demonstrate</Text></Message><Message Date="27.07.2008" Time="21:52:26" DateTime="2008-07-27T19:52:26.828Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Looks good, could you try to add a department 12-13 too?</Text></Message><Message Date="27.07.2008" Time="21:52:41" DateTime="2008-07-27T19:52:41.781Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">Sure, one second</Text></Message><Message Date="27.07.2008" Time="21:53:31" DateTime="2008-07-27T19:53:31.593Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">Done</Text></Message><Message Date="27.07.2008" Time="21:53:38" DateTime="2008-07-27T19:53:38.187Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">This is generic, right?</Text></Message><Message Date="27.07.2008" Time="21:54:36" DateTime="2008-07-27T19:54:36.000Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">What I mean by that, is that I don't need to make changes to the php script later? </Text></Message><Message Date="27.07.2008" Time="21:55:41" DateTime="2008-07-27T19:55:41.781Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">The only reason you would need to make changes to the script is if you changed the layout, such as replacing the "Company name" header with something else</Text></Message><Message Date="27.07.2008" Time="21:56:14" DateTime="2008-07-27T19:56:14.765Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">The PDF will change anytime you modify the database table.  You don't need to change anything for that</Text></Message><Message Date="27.07.2008" Time="21:56:32" DateTime="2008-07-27T19:56:32.625Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Yes, ofcourse. Sounds good. I see the sorting of the departments isn't quite right - 1, 12, 13, 2, 3</Text></Message><Message Date="27.07.2008" Time="21:56:52" DateTime="2008-07-27T19:56:52.265Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">That was a mistake on my part.  I already fixed it</Text></Message><Message Date="27.07.2008" Time="21:57:06" DateTime="2008-07-27T19:57:06.062Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">I made the field text instead of numeric</Text></Message><Message Date="27.07.2008" Time="21:57:11" DateTime="2008-07-27T19:57:11.859Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">I see</Text></Message><Message Date="27.07.2008" Time="21:58:03" DateTime="2008-07-27T19:58:03.984Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Looks good, may I have a look at the script to test it on my end?</Text></Message><Message Date="27.07.2008" Time="21:58:26" DateTime="2008-07-27T19:58:26.531Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">Sure.  One moment</Text></Message><Invitation Date="27.07.2008" Time="22:00:19" DateTime="2008-07-27T20:00:19.671Z" SessionID="1"><From><User FriendlyName="Fred"/></From><File>pdf.php</File><Text Style="color:#545454; ">Fred sends pdf.php</Text></Invitation><InvitationResponse Date="27.07.2008" Time="22:00:57" DateTime="2008-07-27T20:00:57.562Z" SessionID="1"><From><User FriendlyName="Fred"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\pdf.php</File><Text Style="color:#800000; ">You have failed to receive file "pdf.php" from Fred.</Text></InvitationResponse><Message Date="27.07.2008" Time="22:01:26" DateTime="2008-07-27T20:01:26.375Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Hmm, try again</Text></Message><Message Date="27.07.2008" Time="22:01:29" DateTime="2008-07-27T20:01:29.359Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">Should I try to send it again or upload it to ScriptLance?</Text></Message><Message Date="27.07.2008" Time="22:01:34" DateTime="2008-07-27T20:01:34.125Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">ok</Text></Message><Invitation Date="27.07.2008" Time="22:01:39" DateTime="2008-07-27T20:01:39.390Z" SessionID="1"><From><User FriendlyName="Fred"/></From><File>pdf.php</File><Text Style="color:#545454; ">Fred sends pdf.php</Text></Invitation><Message Date="27.07.2008" Time="22:01:53" DateTime="2008-07-27T20:01:53.500Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Oops, doubleclicked</Text></Message><Message Date="27.07.2008" Time="22:02:03" DateTime="2008-07-27T20:02:03.640Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">No problem</Text></Message><Invitation Date="27.07.2008" Time="22:02:08" DateTime="2008-07-27T20:02:08.046Z" SessionID="1"><From><User FriendlyName="Fred"/></From><File>pdf.php</File><Text Style="color:#545454; ">Fred sends pdf.php</Text></Invitation><Message Date="27.07.2008" Time="22:02:39" DateTime="2008-07-27T20:02:39.890Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">Hmm.  I'll upload to ScriptLance</Text></Message><InvitationResponse Date="27.07.2008" Time="22:02:41" DateTime="2008-07-27T20:02:41.859Z" SessionID="1"><From><User FriendlyName="Fred"/></From><File>E:\Privat\Mine Dokumenter\My Received Files\pdf.php</File><Text Style="color:#800000; ">You have failed to receive file "pdf.php" from Fred.</Text></InvitationResponse><Message Date="27.07.2008" Time="22:02:42" DateTime="2008-07-27T20:02:42.609Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">The file transfere in MSN is a bit unstable.. Can't recieve from everyone</Text></Message><Message Date="27.07.2008" Time="22:02:49" DateTime="2008-07-27T20:02:49.625Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Yeah</Text></Message><Message Date="27.07.2008" Time="22:03:49" DateTime="2008-07-27T20:03:49.312Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">https://www.scriptlance.com/f/?121718900651813050</Text></Message><Message Date="27.07.2008" Time="22:14:23" DateTime="2008-07-27T20:14:23.609Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Very nice, works great</Text></Message><Message Date="27.07.2008" Time="22:14:44" DateTime="2008-07-27T20:14:44.812Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">I'll make the escrow payment now :)</Text></Message><Message Date="27.07.2008" Time="22:15:55" DateTime="2008-07-27T20:15:55.593Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">Great, thanks!</Text></Message><Message Date="27.07.2008" Time="22:16:26" DateTime="2008-07-27T20:16:26.000Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fred"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Done! I'm surprised how easy the solution to this was by the way :P 
Thank you very much</Text></Message><Message Date="27.07.2008" Time="22:17:06" DateTime="2008-07-27T20:17:06.265Z" SessionID="1"><From><User FriendlyName="Fred"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg 2; color:#000000; ">No problem, glad to help</Text></Message></Log>

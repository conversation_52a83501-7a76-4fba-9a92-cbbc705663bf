<?xml version="1.0"?>
<?xml-stylesheet type='text/xsl' href='MessageLog.xsl'?>
<Log FirstSessionID="1" LastSessionID="7"><Message Date="15.12.2007" Time="15:50:10" DateTime="2007-12-15T14:50:10.484Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">hello</Text></Message><Message Date="15.12.2007" Time="15:50:27" DateTime="2007-12-15T14:50:27.562Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hai</Text></Message><Message Date="15.12.2007" Time="15:50:27" DateTime="2007-12-15T14:50:27.562Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thanks for the project.</Text></Message><Message Date="15.12.2007" Time="15:50:40" DateTime="2007-12-15T14:50:40.578Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">did u understand the payment method ?</Text></Message><Message Date="15.12.2007" Time="15:50:50" DateTime="2007-12-15T14:50:50.390Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">is there any problem in that ?</Text></Message><Message Date="15.12.2007" Time="15:51:53" DateTime="2007-12-15T14:51:53.546Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yes I did, but I seem to have some problems paying to SL because my paypal account isnt verified, so I wonder - Is it ok if I try to send you 25$ directly from my paypal account (now if you want), and then put 25$ to escrow?</Text></Message><Message Date="15.12.2007" Time="15:52:39" DateTime="2007-12-15T14:52:39.125Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">no problem u can use paypal to send the payment if you have trouble using SL.</Text></Message><Message Date="15.12.2007" Time="15:52:51" DateTime="2007-12-15T14:52:51.718Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">you can pay me via paypal itself</Text></Message><Message Date="15.12.2007" Time="15:52:56" DateTime="2007-12-15T14:52:56.718Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">my paypal <NAME_EMAIL></Text></Message><Message Date="15.12.2007" Time="15:53:00" DateTime="2007-12-15T14:53:00.531Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Ok, which e-mail address do you want me to send to?</Text></Message><Message Date="15.12.2007" Time="15:53:24" DateTime="2007-12-15T14:53:24.484Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Ah, never mind :P</Text></Message><Message Date="15.12.2007" Time="15:53:59" DateTime="2007-12-15T14:53:59.890Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; "><EMAIL></Text></Message><Message Date="15.12.2007" Time="15:54:38" DateTime="2007-12-15T14:54:38.515Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I've sendt 25$ to your account now, then I'll put 25$ on escrow</Text></Message><Message Date="15.12.2007" Time="15:54:53" DateTime="2007-12-15T14:54:53.734Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok thanks for that.</Text></Message><Message Date="15.12.2007" Time="15:55:03" DateTime="2007-12-15T14:55:03.937Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">can i have the psd files now please.</Text></Message><Message Date="15.12.2007" Time="15:55:07" DateTime="2007-12-15T14:55:07.859Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">2 sec</Text></Message><Message Date="15.12.2007" Time="15:55:23" DateTime="2007-12-15T14:55:23.062Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Invitation Date="15.12.2007" Time="15:55:45" DateTime="2007-12-15T14:55:45.593Z" SessionID="1"><From><User FriendlyName="Jh"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\test2.psd</File><Text Style="color:#545454; ">Jh sends C:\Documents and Settings\Administrator\Skrivebord\test2.psd</Text></Invitation><Message Date="15.12.2007" Time="15:56:16" DateTime="2007-12-15T14:56:16.281Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i'm here.</Text></Message><Message Date="15.12.2007" Time="15:56:37" DateTime="2007-12-15T14:56:37.453Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I'll upload it to the web instead, my msn transfere is so slow</Text></Message><Message Date="15.12.2007" Time="15:56:52" DateTime="2007-12-15T14:56:52.796Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok fine.</Text></Message><Message Date="15.12.2007" Time="15:56:52" DateTime="2007-12-15T14:56:52.812Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thanks.</Text></Message><Message Date="15.12.2007" Time="15:57:17" DateTime="2007-12-15T14:57:17.734Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Here you go:
http://www.zshare.net/download/5643191216c46d/</Text></Message><Message Date="15.12.2007" Time="16:00:08" DateTime="2007-12-15T15:00:08.250Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Did you fetch it?</Text></Message><Message Date="15.12.2007" Time="16:00:23" DateTime="2007-12-15T15:00:23.515Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yes</Text></Message><Message Date="15.12.2007" Time="16:00:23" DateTime="2007-12-15T15:00:23.531Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">downloading.</Text></Message><Message Date="15.12.2007" Time="16:00:41" DateTime="2007-12-15T15:00:41.921Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Nice :) </Text></Message><Message Date="15.12.2007" Time="16:02:14" DateTime="2007-12-15T15:02:14.125Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok i have some questions.</Text></Message><Message Date="15.12.2007" Time="16:02:14" DateTime="2007-12-15T15:02:14.125Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">got the file.</Text></Message><Message Date="15.12.2007" Time="16:02:57" DateTime="2007-12-15T15:02:57.625Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yes, fire out :)</Text></Message><Message Date="15.12.2007" Time="16:03:23" DateTime="2007-12-15T15:03:23.015Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">you just want the content part to grow.</Text></Message><Message Date="15.12.2007" Time="16:03:23" DateTime="2007-12-15T15:03:23.031Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">is that it ?</Text></Message><Message Date="15.12.2007" Time="16:04:43" DateTime="2007-12-15T15:04:43.203Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Well, the answere is probably not.. But I'd like to have the possibility. Are you asking because I wanted it dynamic? </Text></Message><Message Date="15.12.2007" Time="16:05:08" DateTime="2007-12-15T15:05:08.390Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yes</Text></Message><Message Date="15.12.2007" Time="16:05:49" DateTime="2007-12-15T15:05:49.171Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">what are the two horizontal blue bars on both sides. Are they borders of you need those two bars as in the psd file ?</Text></Message><Message Date="15.12.2007" Time="16:06:03" DateTime="2007-12-15T15:06:03.015Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">sorry vertical.</Text></Message><Message Date="15.12.2007" Time="16:06:15" DateTime="2007-12-15T15:06:15.062Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">That's just to indicate the color I want on the background</Text></Message><Message Date="15.12.2007" Time="16:06:45" DateTime="2007-12-15T15:06:45.078Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I want the site itself to be centered, with #18426e as the background color</Text></Message><Message Date="15.12.2007" Time="16:07:05" DateTime="2007-12-15T15:07:05.484Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok fine.</Text></Message><Message Date="15.12.2007" Time="16:07:15" DateTime="2007-12-15T15:07:15.718Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thanks </Text></Message><Message Date="15.12.2007" Time="16:07:15" DateTime="2007-12-15T15:07:15.718Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">that's all</Text></Message><Message Date="15.12.2007" Time="16:07:25" DateTime="2007-12-15T15:07:25.921Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">see u soon.</Text></Message><Message Date="15.12.2007" Time="16:07:25" DateTime="2007-12-15T15:07:25.921Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">bye.</Text></Message><Message Date="15.12.2007" Time="16:07:56" DateTime="2007-12-15T15:07:56.843Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Great, bye - just send a mail or IM here when you got any questions, or when your finished :)</Text></Message><Message Date="15.12.2007" Time="16:08:12" DateTime="2007-12-15T15:08:12.062Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Message Date="15.12.2007" Time="16:50:38" DateTime="2007-12-15T15:50:38.140Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hai</Text></Message><Message Date="15.12.2007" Time="16:50:38" DateTime="2007-12-15T15:50:38.140Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">r u there ?</Text></Message><Message Date="15.12.2007" Time="16:52:39" DateTime="2007-12-15T15:52:39.593Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hai r u there ?</Text></Message><Message Date="15.12.2007" Time="16:54:14" DateTime="2007-12-15T15:54:14.359Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">yes'ir</Text></Message><Message Date="15.12.2007" Time="16:54:28" DateTime="2007-12-15T15:54:28.953Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I'm here :)</Text></Message><Message Date="15.12.2007" Time="16:54:34" DateTime="2007-12-15T15:54:34.156Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">your project is complete.</Text></Message><Message Date="15.12.2007" Time="16:54:44" DateTime="2007-12-15T15:54:44.375Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">shall i send u now ?</Text></Message><Message Date="15.12.2007" Time="16:54:53" DateTime="2007-12-15T15:54:53.218Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">That was fast</Text></Message><Message Date="15.12.2007" Time="16:54:56" DateTime="2007-12-15T15:54:56.859Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yes please</Text></Message><Message Date="15.12.2007" Time="16:56:41" DateTime="2007-12-15T15:56:41.015Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">pls accept.</Text></Message><Invitation Date="15.12.2007" Time="16:56:41" DateTime="2007-12-15T15:56:41.078Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><File>kontorland.zip</File><Text Style="color:#545454; "><EMAIL> sends kontorland.zip</Text></Invitation><InvitationResponse Date="15.12.2007" Time="16:58:11" DateTime="2007-12-15T15:58:11.015Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\kontorland.zip</File><Text Style="color:#800000; ">You have failed to receive file "kontorland.zip" from <EMAIL>.</Text></InvitationResponse><Message Date="15.12.2007" Time="16:58:36" DateTime="2007-12-15T15:58:36.984Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">sorry got disconnected.</Text></Message><Invitation Date="15.12.2007" Time="16:58:37" DateTime="2007-12-15T15:58:37.000Z" SessionID="1"><From><User FriendlyName="<EMAIL>"/></From><File>kontorland.zip</File><Text Style="color:#545454; "><EMAIL> sends kontorland.zip</Text></Invitation><Message Date="15.12.2007" Time="16:58:53" DateTime="2007-12-15T15:58:53.421Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">try again</Text></Message><Message Date="15.12.2007" Time="16:59:33" DateTime="2007-12-15T15:59:33.968Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Maybe it's better to upload it to http://www.zshare.net/ ?</Text></Message><Message Date="15.12.2007" Time="16:59:40" DateTime="2007-12-15T15:59:40.015Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah</Text></Message><Message Date="15.12.2007" Time="16:59:40" DateTime="2007-12-15T15:59:40.015Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">pls accept .</Text></Message><Message Date="15.12.2007" Time="16:59:49" DateTime="2007-12-15T15:59:49.171Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">no its only 80kb</Text></Message><Message Date="15.12.2007" Time="16:59:54" DateTime="2007-12-15T15:59:54.734Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Send again</Text></Message><Invitation Date="15.12.2007" Time="17:00:29" DateTime="2007-12-15T16:00:29.953Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><File>kontorland.zip</File><Text Style="color:#545454; "><EMAIL> sends kontorland.zip</Text></Invitation><Message Date="15.12.2007" Time="17:02:08" DateTime="2007-12-15T16:02:08.593Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">if all are ok.</Text></Message><Message Date="15.12.2007" Time="17:02:29" DateTime="2007-12-15T16:02:29.031Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">pls drop me an excellent review and some best words which will fetch me more works and i'll do the same for you.</Text></Message><InvitationResponse Date="15.12.2007" Time="17:02:59" DateTime="2007-12-15T16:02:59.781Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><File>E:\Privat\Mine Dokumenter\My Received Files\kontorland.zip</File><Text Style="color:#800000; ">The virus scan could not be completed. Please check your settings in Tools &gt; Options &gt; File Transfer.</Text></InvitationResponse><Message Date="15.12.2007" Time="17:05:59" DateTime="2007-12-15T16:05:59.671Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yeah, I hope to do so - we'll need to get finished first :) It's not dynamic, and the text in IE 6.0. looks terrible (screenshot: http://www.zshare.net/image/5644865060d364/ )</Text></Message><Message Date="15.12.2007" Time="17:07:21" DateTime="2007-12-15T16:07:21.984Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ouch</Text></Message><Message Date="15.12.2007" Time="17:07:22" DateTime="2007-12-15T16:07:22.000Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok i'll clear that.</Text></Message><Message Date="15.12.2007" Time="17:08:06" DateTime="2007-12-15T16:08:06.218Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">:)</Text></Message><Message Date="15.12.2007" Time="17:08:21" DateTime="2007-12-15T16:08:21.421Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">IE sucks</Text></Message><Message Date="15.12.2007" Time="17:08:28" DateTime="2007-12-15T16:08:28.406Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yes, but people still use it</Text></Message><Message Date="15.12.2007" Time="17:08:33" DateTime="2007-12-15T16:08:33.609Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but its fine on IE7</Text></Message><Message Date="15.12.2007" Time="17:08:33" DateTime="2007-12-15T16:08:33.625Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">no problem</Text></Message><Message Date="15.12.2007" Time="17:08:43" DateTime="2007-12-15T16:08:43.812Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i'll check that.</Text></Message><Message Date="15.12.2007" Time="17:09:59" DateTime="2007-12-15T16:09:59.390Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Hum, I see now that the text-thing in I.E.6 is because of the text-size</Text></Message><Message Date="15.12.2007" Time="17:10:06" DateTime="2007-12-15T16:10:06.312Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">In i.e</Text></Message><Message Date="15.12.2007" Time="17:12:13" DateTime="2007-12-15T16:12:13.515Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">But anyhow, the most important thing is to get the height to be a 100% in the webbrowser - So that the footer will allway stay on the bottom of the page</Text></Message><Message Date="15.12.2007" Time="17:12:29" DateTime="2007-12-15T16:12:29.375Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">And the header at the top</Text></Message><Message Date="15.12.2007" Time="17:12:34" DateTime="2007-12-15T16:12:34.546Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Message Date="15.12.2007" Time="17:12:34" DateTime="2007-12-15T16:12:34.562Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Message Date="15.12.2007" Time="17:15:04" DateTime="2007-12-15T16:15:04.390Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Message Date="15.12.2007" Time="17:15:04" DateTime="2007-12-15T16:15:04.390Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i've finished.</Text></Message><Message Date="15.12.2007" Time="17:15:16" DateTime="2007-12-15T16:15:16.265Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Alright</Text></Message><Invitation Date="15.12.2007" Time="17:15:38" DateTime="2007-12-15T16:15:38.906Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><File>kontorland2.zip</File><Text Style="color:#545454; "><EMAIL> sends kontorland2.zip</Text></Invitation><Message Date="15.12.2007" Time="17:15:47" DateTime="2007-12-15T16:15:47.203Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">pls check this and let me know if any problems.</Text></Message><InvitationResponse Date="15.12.2007" Time="17:18:11" DateTime="2007-12-15T16:18:11.656Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><File>E:\Privat\Mine Dokumenter\My Received Files\kontorland2.zip</File><Text Style="color:#800000; ">The virus scan could not be completed. Please check your settings in Tools &gt; Options &gt; File Transfer.</Text></InvitationResponse><Message Date="15.12.2007" Time="17:19:44" DateTime="2007-12-15T16:19:44.828Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I don't see any difference, I'll try to explain a bit better..</Text></Message><Message Date="15.12.2007" Time="17:20:04" DateTime="2007-12-15T16:20:04.046Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok please.</Text></Message><Message Date="15.12.2007" Time="17:21:11" DateTime="2007-12-15T16:21:11.250Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I gotta leave now in 2 min.. Just so you know..
Lets say you don't write more than five line in the left and right content areas, then the footer won't reach the bottom of the page</Text></Message><Message Date="15.12.2007" Time="17:21:32" DateTime="2007-12-15T16:21:32.296Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">So, instead.. The footer will be on the middle of the page</Text></Message><Message Date="15.12.2007" Time="17:21:42" DateTime="2007-12-15T16:21:42.593Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Message Date="15.12.2007" Time="17:21:47" DateTime="2007-12-15T16:21:47.578Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">so is this problem on ie 6</Text></Message><Message Date="15.12.2007" Time="17:21:52" DateTime="2007-12-15T16:21:52.593Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i understand now.</Text></Message><Message Date="15.12.2007" Time="17:21:57" DateTime="2007-12-15T16:21:57.265Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">No, in opera too</Text></Message><Message Date="15.12.2007" Time="17:22:07" DateTime="2007-12-15T16:22:07.687Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">aha.</Text></Message><Message Date="15.12.2007" Time="17:22:31" DateTime="2007-12-15T16:22:31.265Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">could u please tell me ur os and opera version ?</Text></Message><Message Date="15.12.2007" Time="17:22:53" DateTime="2007-12-15T16:22:53.421Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">V. 8.54

As you see on this one, the footer will allways be at the bottom (but this is with tables)
http://www.tumleplassen.net/upload/test/slett.html</Text></Message><Message Date="15.12.2007" Time="17:22:53" DateTime="2007-12-15T16:22:53.640Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">because i dont have ie6 </Text></Message><Message Date="15.12.2007" Time="17:23:01" DateTime="2007-12-15T16:23:01.484Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but i can manage opera.</Text></Message><Message Date="15.12.2007" Time="17:23:07" DateTime="2007-12-15T16:23:07.234Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">But I really need to go</Text></Message><Message Date="15.12.2007" Time="17:23:19" DateTime="2007-12-15T16:23:19.125Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Message Date="15.12.2007" Time="17:23:22" DateTime="2007-12-15T16:23:22.562Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Try to get the footer to be at the bottom , and the header at the top :)</Text></Message><Message Date="15.12.2007" Time="17:23:39" DateTime="2007-12-15T16:23:39.875Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">See you later then</Text></Message><Message Date="15.12.2007" Time="17:23:50" DateTime="2007-12-15T16:23:50.296Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i'll check everything and email u the completed one soon.</Text></Message><Message Date="15.12.2007" Time="17:23:50" DateTime="2007-12-15T16:23:50.296Z" SessionID="2"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">bye.</Text></Message><Message Date="16.12.2007" Time="16:03:08" DateTime="2007-12-16T15:03:08.218Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Hello</Text></Message><Message Date="16.12.2007" Time="16:03:25" DateTime="2007-12-16T15:03:25.343Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hai</Text></Message><Message Date="16.12.2007" Time="16:03:25" DateTime="2007-12-16T15:03:25.375Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">how r u ?</Text></Message><Message Date="16.12.2007" Time="16:03:35" DateTime="2007-12-16T15:03:35.593Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">did u receive the mail sir.</Text></Message><Message Date="16.12.2007" Time="16:03:45" DateTime="2007-12-16T15:03:45.484Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Fine thanks, and you?</Text></Message><Message Date="16.12.2007" Time="16:03:51" DateTime="2007-12-16T15:03:51.687Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yes, I'm looking at right now</Text></Message><Message Date="16.12.2007" Time="16:03:57" DateTime="2007-12-16T15:03:57.031Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i am fine too.</Text></Message><Message Date="16.12.2007" Time="16:04:11" DateTime="2007-12-16T15:04:11.031Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Message Date="16.12.2007" Time="16:04:23" DateTime="2007-12-16T15:04:23.906Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I wonder what resolution are you running on your computer?</Text></Message><Message Date="16.12.2007" Time="16:04:39" DateTime="2007-12-16T15:04:39.156Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">1024x768</Text></Message><Message Date="16.12.2007" Time="16:05:12" DateTime="2007-12-16T15:05:12.234Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I thought so, try setting it to 1280x1024 - then view the webpage with that resolution</Text></Message><Message Date="16.12.2007" Time="16:05:12" DateTime="2007-12-16T15:05:12.484Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">is there any problem.</Text></Message><Message Date="16.12.2007" Time="16:05:53" DateTime="2007-12-16T15:05:53.187Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">my monitor does'nt support that resolution.</Text></Message><Message Date="16.12.2007" Time="16:06:23" DateTime="2007-12-16T15:06:23.656Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i have send u the browsershots links</Text></Message><Message Date="16.12.2007" Time="16:06:34" DateTime="2007-12-16T15:06:34.125Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">please check that.</Text></Message><Message Date="16.12.2007" Time="16:08:20" DateTime="2007-12-16T15:08:20.984Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">everything is ok on my pc and I told to check the page on my friend's computer </Text></Message><Message Date="16.12.2007" Time="16:08:21" DateTime="2007-12-16T15:08:21.000Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">all are fine.</Text></Message><Message Date="16.12.2007" Time="16:08:31" DateTime="2007-12-16T15:08:31.312Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">and the browsershots you can see that.</Text></Message><Message Date="16.12.2007" Time="16:13:21" DateTime="2007-12-16T15:13:21.718Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Ofcourse the browsershots it ok, and will be ok on any computer with 1024x768 in resolution.. I just found out why, it's because you've used pixels as the definition on minheight on #divContentLeft, #divContentRight</Text></Message><Message Date="16.12.2007" Time="16:14:05" DateTime="2007-12-16T15:14:05.812Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">What if we used percent instead?</Text></Message><Message Date="16.12.2007" Time="16:14:41" DateTime="2007-12-16T15:14:41.453Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok let me try that.</Text></Message><Message Date="16.12.2007" Time="16:14:41" DateTime="2007-12-16T15:14:41.468Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">pls wait.</Text></Message><Message Date="16.12.2007" Time="16:17:07" DateTime="2007-12-16T15:17:07.015Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">sir it look normal on my friends pc with the resolution u said.</Text></Message><Message Date="16.12.2007" Time="16:17:17" DateTime="2007-12-16T15:17:17.234Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i think the trouble is in ur pc.</Text></Message><Message Date="16.12.2007" Time="16:18:22" DateTime="2007-12-16T15:18:22.062Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Ok, I'll test it on the server on my work - give me 2 min</Text></Message><Message Date="16.12.2007" Time="16:18:37" DateTime="2007-12-16T15:18:37.265Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Message Date="16.12.2007" Time="16:18:37" DateTime="2007-12-16T15:18:37.265Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thanks</Text></Message><Message Date="16.12.2007" Time="16:25:37" DateTime="2007-12-16T15:25:37.484Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Exactly the same problem - You do know what I mean, right? I mean that the site isn’t 100% in height, so the higher resolution yo have, the longer up the footer will be. This is not a browser problem, you need to set a definition in your code.</Text></Message><Message Date="16.12.2007" Time="16:26:03" DateTime="2007-12-16T15:26:03.828Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">And by the way, I do know what I'm talking about, so please don't say that this is a problem on my computer again. I daily work as an IT consultant, and I do know a bit about coding in general, it’s just that I leave this stuff for those who have this area in expertise.</Text></Message><Message Date="16.12.2007" Time="16:26:29" DateTime="2007-12-16T15:26:29.843Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">pls wait sir.</Text></Message><Message Date="16.12.2007" Time="16:27:00" DateTime="2007-12-16T15:27:00.140Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">And again, I don't mean to be rude - It's just that you need to understand what I mean, instead of saying that this is a problem on my side</Text></Message><Message Date="16.12.2007" Time="16:27:15" DateTime="2007-12-16T15:27:15.359Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i'll provide a screenshot from my friend's computer with the resolution u gave me.</Text></Message><Message Date="16.12.2007" Time="16:27:45" DateTime="2007-12-16T15:27:45.796Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok let me see where the problem exactly is.</Text></Message><Message Date="16.12.2007" Time="16:28:26" DateTime="2007-12-16T15:28:26.406Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">what os are u using sir ?</Text></Message><Message Date="16.12.2007" Time="16:28:51" DateTime="2007-12-16T15:28:51.593Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Windows XP, tried on 2000 and Windows 2003 server too</Text></Message><Message Date="16.12.2007" Time="16:29:02" DateTime="2007-12-16T15:29:02.015Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">aha</Text></Message><Message Date="16.12.2007" Time="16:29:12" DateTime="2007-12-16T15:29:12.234Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok let me check</Text></Message><Message Date="16.12.2007" Time="16:32:42" DateTime="2007-12-16T15:32:42.765Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Check this link, he explains how to archieve what I want:
http://www.sitepoint.com/forums/showpost.php?p=1243541&amp;postcount=8</Text></Message><Message Date="16.12.2007" Time="16:32:46" DateTime="2007-12-16T15:32:46.562Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok the problem is with the resolution u said</Text></Message><Message Date="16.12.2007" Time="16:32:46" DateTime="2007-12-16T15:32:46.578Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i see that now.</Text></Message><Message Date="16.12.2007" Time="16:32:57" DateTime="2007-12-16T15:32:57.984Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i'll check that and let u know.</Text></Message><Message Date="16.12.2007" Time="16:33:08" DateTime="2007-12-16T15:33:08.187Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thanks for that.</Text></Message><Message Date="16.12.2007" Time="16:33:13" DateTime="2007-12-16T15:33:13.281Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Great, thanks</Text></Message><Message Date="16.12.2007" Time="17:19:22" DateTime="2007-12-16T16:19:22.531Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hi</Text></Message><Message Date="16.12.2007" Time="17:19:22" DateTime="2007-12-16T16:19:22.546Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">r u there ?</Text></Message><Message Date="16.12.2007" Time="17:19:30" DateTime="2007-12-16T16:19:30.656Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yes'sir</Text></Message><Message Date="16.12.2007" Time="17:20:46" DateTime="2007-12-16T16:20:46.718Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">please check this page and send me a screenshot because my monitor does'nt support the resolution u mentioned.</Text></Message><Message Date="16.12.2007" Time="17:20:46" DateTime="2007-12-16T16:20:46.734Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">http://www.smaartweb.com/kontorland2/</Text></Message><Message Date="16.12.2007" Time="17:21:39" DateTime="2007-12-16T16:21:39.281Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">It's the same problem - I uploaded the screenshots here:
http://browsershots.org/http://www.tumleplassen.net/upload/kontorland2/</Text></Message><Message Date="16.12.2007" Time="17:25:43" DateTime="2007-12-16T16:25:43.406Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">can I ask u something ?</Text></Message><Message Date="16.12.2007" Time="17:25:49" DateTime="2007-12-16T16:25:49.312Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Sure</Text></Message><Message Date="16.12.2007" Time="17:26:55" DateTime="2007-12-16T16:26:55.109Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">do u want the page optimised for 1280px wide screen ?</Text></Message><Message Date="16.12.2007" Time="17:27:53" DateTime="2007-12-16T16:27:53.468Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I want the page look fine independent if what resolution they use</Text></Message><Message Date="16.12.2007" Time="17:28:54" DateTime="2007-12-16T16:28:54.234Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">But fact is, people usually use either 1024 or 1280 in width</Text></Message><Message Date="16.12.2007" Time="17:29:29" DateTime="2007-12-16T16:29:29.890Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">I asked this because there is no solution for this using css as css does'nt have any such things as resolution checking but can be achieved using javascript.</Text></Message><Message Date="16.12.2007" Time="17:29:40" DateTime="2007-12-16T16:29:40.125Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">is that ok for you sir.</Text></Message><Message Date="16.12.2007" Time="17:29:56" DateTime="2007-12-16T16:29:56.890Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I see, I didn't know that</Text></Message><Message Date="16.12.2007" Time="17:30:17" DateTime="2007-12-16T16:30:17.843Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">So what does people usually do in these cases?</Text></Message><Message Date="16.12.2007" Time="17:30:33" DateTime="2007-12-16T16:30:33.281Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">the last thing is to use em based magnification</Text></Message><Message Date="16.12.2007" Time="17:30:53" DateTime="2007-12-16T16:30:53.687Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">and my last hack was that and it did'nt work.</Text></Message><Message Date="16.12.2007" Time="17:31:34" DateTime="2007-12-16T16:31:34.687Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">so the only thing we can do is adjust the sites height for 1280px and it will look fine.</Text></Message><Message Date="16.12.2007" Time="17:32:15" DateTime="2007-12-16T16:32:15.500Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but on resolutions lower than that the page extends a little bit lower and the page height will be taller than the 1280px screen.</Text></Message><Message Date="16.12.2007" Time="17:32:25" DateTime="2007-12-16T16:32:25.703Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">do u want to check that how it looks like ?</Text></Message><Message Date="16.12.2007" Time="17:33:16" DateTime="2007-12-16T16:33:16.937Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I know how it looks like, but if you use the javascript - will users with I.E be prompted?</Text></Message><Message Date="16.12.2007" Time="17:35:05" DateTime="2007-12-16T16:35:05.234Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">no</Text></Message><Message Date="16.12.2007" Time="17:35:12" DateTime="2007-12-16T16:35:12.812Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but will look ok on IE too.</Text></Message><Message Date="16.12.2007" Time="17:35:27" DateTime="2007-12-16T16:35:27.843Z" SessionID="3"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Then I'd like you to use java to achieve that :)</Text></Message><Message Date="16.12.2007" Time="17:35:53" DateTime="2007-12-16T16:35:53.046Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Message Date="16.12.2007" Time="17:35:53" DateTime="2007-12-16T16:35:53.062Z" SessionID="3"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">let me check that now.</Text></Message><Message Date="16.12.2007" Time="21:29:28" DateTime="2007-12-16T20:29:28.468Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Hello, how did it work out with java?</Text></Message><Message Date="16.12.2007" Time="21:29:55" DateTime="2007-12-16T20:29:55.734Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i'm trying to achieve using complete css</Text></Message><Message Date="16.12.2007" Time="21:30:05" DateTime="2007-12-16T20:30:05.921Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i am checking it.</Text></Message><Message Date="16.12.2007" Time="21:30:46" DateTime="2007-12-16T20:30:46.578Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">can u check and tell the result ?</Text></Message><Message Date="16.12.2007" Time="21:30:52" DateTime="2007-12-16T20:30:52.437Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Sure :)</Text></Message><Message Date="16.12.2007" Time="21:33:09" DateTime="2007-12-16T20:33:09.171Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">http://www.smaartweb.com/test/</Text></Message><Message Date="16.12.2007" Time="21:33:39" DateTime="2007-12-16T20:33:39.812Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">this is the first time i am working around a css file like this.</Text></Message><Message Date="16.12.2007" Time="21:33:39" DateTime="2007-12-16T20:33:39.828Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but its very interesting.</Text></Message><Message Date="16.12.2007" Time="21:34:57" DateTime="2007-12-16T20:34:57.828Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Hey, look at that! Very nice, very nice</Text></Message><Message Date="16.12.2007" Time="21:35:21" DateTime="2007-12-16T20:35:21.546Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">How did you achieve it?</Text></Message><Message Date="16.12.2007" Time="21:36:08" DateTime="2007-12-16T20:36:08.890Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">what happened ?</Text></Message><Message Date="16.12.2007" Time="21:36:51" DateTime="2007-12-16T20:36:51.421Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">It looks good, you've achieved what I wanted :)</Text></Message><Message Date="16.12.2007" Time="21:37:03" DateTime="2007-12-16T20:37:03.171Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">OMG</Text></Message><Message Date="16.12.2007" Time="21:37:28" DateTime="2007-12-16T20:37:28.703Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">The footer is at the bottom of the page no matte what resolution I use</Text></Message><Message Date="16.12.2007" Time="21:37:32" DateTime="2007-12-16T20:37:32.656Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but i am sure there is no such information available to achieve this result on the internet.</Text></Message><Message Date="16.12.2007" Time="21:37:52" DateTime="2007-12-16T20:37:52.640Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i have to post this immediately if its working.</Text></Message><Message Date="16.12.2007" Time="21:38:00" DateTime="2007-12-16T20:38:00.281Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Hehe, what did you do?</Text></Message><Message Date="16.12.2007" Time="21:38:15" DateTime="2007-12-16T20:38:15.468Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">a simple trick</Text></Message><Message Date="16.12.2007" Time="21:38:26" DateTime="2007-12-16T20:38:26.171Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">read the css u will understand.</Text></Message><Message Date="16.12.2007" Time="21:38:30" DateTime="2007-12-16T20:38:30.953Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">I think I have a slight idea already</Text></Message><Message Date="16.12.2007" Time="21:38:51" DateTime="2007-12-16T20:38:51.593Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">can u share with me ?</Text></Message><Message Date="16.12.2007" Time="21:40:32" DateTime="2007-12-16T20:40:32.843Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Well, due to the fact that the no matter what resolution I use, the footer will allways be 43px down - So I think you've managed to set the body to go a 100%, and then add 43px so the footer will be visible</Text></Message><Message Date="16.12.2007" Time="21:40:58" DateTime="2007-12-16T20:40:58.234Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">no not that.</Text></Message><Message Date="16.12.2007" Time="21:41:08" DateTime="2007-12-16T20:41:08.453Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">that was what originally done before.</Text></Message><Message Date="16.12.2007" Time="21:41:08" DateTime="2007-12-16T20:41:08.453Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but we did not get the result.</Text></Message><Message Date="16.12.2007" Time="21:41:18" DateTime="2007-12-16T20:41:18.656Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">first i took off the footer from the main wrapper.</Text></Message><Message Date="16.12.2007" Time="21:41:28" DateTime="2007-12-16T20:41:28.859Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">and styled it individually</Text></Message><Message Date="16.12.2007" Time="21:41:49" DateTime="2007-12-16T20:41:49.234Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">and centered it.</Text></Message><Message Date="16.12.2007" Time="21:42:19" DateTime="2007-12-16T20:42:19.843Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">already a 100% height was given to the body and html tags</Text></Message><Message Date="16.12.2007" Time="21:42:20" DateTime="2007-12-16T20:42:20.093Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but it did not solve the problem</Text></Message><Message Date="16.12.2007" Time="21:42:50" DateTime="2007-12-16T20:42:50.906Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">so i went to change the height of the content wrapper alone to height 100%.</Text></Message><Message Date="16.12.2007" Time="21:43:01" DateTime="2007-12-16T20:43:01.109Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">wow that worked.</Text></Message><Message Date="16.12.2007" Time="21:43:11" DateTime="2007-12-16T20:43:11.312Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but i already coded a javascript for this.</Text></Message><Message Date="16.12.2007" Time="21:43:11" DateTime="2007-12-16T20:43:11.328Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">that's now useless.</Text></Message><Message Date="16.12.2007" Time="21:43:34" DateTime="2007-12-16T20:43:34.437Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">why do we want javascript it everything works with css.</Text></Message><Message Date="16.12.2007" Time="21:43:49" DateTime="2007-12-16T20:43:49.437Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">why do we want javascript if everything works with css.</Text></Message><Message Date="16.12.2007" Time="21:43:54" DateTime="2007-12-16T20:43:54.171Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Nice and clean solution to the problem :)</Text></Message><Message Date="16.12.2007" Time="21:44:17" DateTime="2007-12-16T20:44:17.625Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thank u for ur support sir,</Text></Message><Message Date="16.12.2007" Time="21:44:22" DateTime="2007-12-16T20:44:22.609Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">without u i would have never achieved this.</Text></Message><Message Date="16.12.2007" Time="21:44:22" DateTime="2007-12-16T20:44:22.937Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">"Because of my lack of knowledge about CSS, maryam had a challenging task achieving what I wanted due to the fact that CSS have no such function as the one I wanted. But maryam totally went out of his way to make me happy, and solved the task the best way possible. Very professional and responsive to any queries. Beautiful finished project!"</Text></Message><Message Date="16.12.2007" Time="21:44:46" DateTime="2007-12-16T20:44:46.078Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Anything you want me to add to this?</Text></Message><Message Date="16.12.2007" Time="21:45:11" DateTime="2007-12-16T20:45:11.468Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">u were very patient.</Text></Message><Message Date="16.12.2007" Time="21:45:11" DateTime="2007-12-16T20:45:11.468Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">wow</Text></Message><Message Date="16.12.2007" Time="21:45:21" DateTime="2007-12-16T20:45:21.671Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">that's more than enough.</Text></Message><Message Date="16.12.2007" Time="21:45:34" DateTime="2007-12-16T20:45:34.156Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Thanks the same :)</Text></Message><Message Date="16.12.2007" Time="21:45:49" DateTime="2007-12-16T20:45:49.656Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Beautiful finished project!"
</Text></Message><Message Date="16.12.2007" Time="21:45:59" DateTime="2007-12-16T20:45:59.859Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">is there any mistake in that ?</Text></Message><Message Date="16.12.2007" Time="21:46:20" DateTime="2007-12-16T20:46:20.265Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">because my english is bad u know.</Text></Message><Message Date="16.12.2007" Time="21:47:16" DateTime="2007-12-16T20:47:16.187Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Nope, just checked - It's spelled right ;)</Text></Message><Message Date="16.12.2007" Time="21:47:31" DateTime="2007-12-16T20:47:31.375Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok thank u.</Text></Message><Message Date="16.12.2007" Time="21:47:41" DateTime="2007-12-16T20:47:41.593Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">r u from norway ?</Text></Message><Message Date="16.12.2007" Time="21:47:53" DateTime="2007-12-16T20:47:53.625Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yes</Text></Message><Message Date="16.12.2007" Time="21:48:19" DateTime="2007-12-16T20:48:19.031Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">so u r 4.30 behind my local time.</Text></Message><Message Date="16.12.2007" Time="21:49:08" DateTime="2007-12-16T20:49:08.750Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">That may be right, the time is 09:49 PM</Text></Message><Message Date="16.12.2007" Time="21:49:23" DateTime="2007-12-16T20:49:23.984Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">here it is 2.16 am.</Text></Message><Message Date="16.12.2007" Time="21:49:35" DateTime="2007-12-16T20:49:35.671Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Spot on then :)</Text></Message><Message Date="16.12.2007" Time="21:49:46" DateTime="2007-12-16T20:49:46.093Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i am still awake </Text></Message><Message Date="16.12.2007" Time="21:49:56" DateTime="2007-12-16T20:49:56.312Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but ur project was a bit interesting.</Text></Message><Message Date="16.12.2007" Time="21:50:28" DateTime="2007-12-16T20:50:28.796Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">That's good to hear that both of us got something good out of this :)</Text></Message><Message Date="16.12.2007" Time="21:50:30" DateTime="2007-12-16T20:50:30.578Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">really got some new knowledge.</Text></Message><Message Date="16.12.2007" Time="21:50:32" DateTime="2007-12-16T20:50:32.531Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Great effort!</Text></Message><Message Date="16.12.2007" Time="21:50:47" DateTime="2007-12-16T20:50:47.937Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thanks for your appreciation.</Text></Message><Message Date="16.12.2007" Time="21:50:58" DateTime="2007-12-16T20:50:58.156Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">if u need any help do not hesitate to contact me.</Text></Message><Message Date="16.12.2007" Time="21:51:04" DateTime="2007-12-16T20:51:04.265Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i loved working with you.</Text></Message><Message Date="16.12.2007" Time="21:51:14" DateTime="2007-12-16T20:51:14.468Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i have dropped you an excellent review too.</Text></Message><Message Date="16.12.2007" Time="21:51:45" DateTime="2007-12-16T20:51:45.343Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">No doubt, same to you! Oh, and by the way, do you send or upload the files?</Text></Message><Message Date="16.12.2007" Time="21:51:55" DateTime="2007-12-16T20:51:55.781Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">do u know how to review me ?</Text></Message><Message Date="16.12.2007" Time="21:51:59" DateTime="2007-12-16T20:51:59.171Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">oh i forgot that.</Text></Message><Message Date="16.12.2007" Time="21:52:04" DateTime="2007-12-16T20:52:04.171Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">pls wait.</Text></Message><Message Date="16.12.2007" Time="21:52:07" DateTime="2007-12-16T20:52:07.265Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yeah</Text></Message><Invitation Date="16.12.2007" Time="21:52:42" DateTime="2007-12-16T20:52:42.515Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><File>kontorlandfinal.rar</File><Text Style="color:#545454; "><EMAIL> sends kontorlandfinal.rar</Text></Invitation><InvitationResponse Date="16.12.2007" Time="21:54:20" DateTime="2007-12-16T20:54:20.921Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><File>E:\Privat\Mine Dokumenter\My Received Files\kontorlandfinal.rar</File><Text Style="color:#800000; ">The virus scan could not be completed. Please check your settings in Tools &gt; Options &gt; File Transfer.</Text></InvitationResponse><Message Date="16.12.2007" Time="21:56:48" DateTime="2007-12-16T20:56:48.906Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Thanks again - I've dropped you the review, and completed the escrow payment</Text></Message><Message Date="16.12.2007" Time="21:57:14" DateTime="2007-12-16T20:57:14.328Z" SessionID="4"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thanks for that.</Text></Message><Message Date="16.12.2007" Time="21:59:38" DateTime="2007-12-16T20:59:38.015Z" SessionID="5"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">see u later</Text></Message><Message Date="16.12.2007" Time="21:59:38" DateTime="2007-12-16T20:59:38.031Z" SessionID="5"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">bye.</Text></Message><Message Date="16.12.2007" Time="21:59:44" DateTime="2007-12-16T20:59:44.078Z" SessionID="5"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yea, take care</Text></Message><Message Date="16.12.2007" Time="22:00:09" DateTime="2007-12-16T21:00:09.359Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">sorry</Text></Message><Message Date="16.12.2007" Time="22:00:29" DateTime="2007-12-16T21:00:29.796Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i have to ask u about the other project u posted.</Text></Message><Message Date="16.12.2007" Time="22:00:39" DateTime="2007-12-16T21:00:39.984Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">what is that all about ?</Text></Message><Message Date="16.12.2007" Time="22:00:44" DateTime="2007-12-16T21:00:44.375Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">PHP/MySQL?</Text></Message><Message Date="16.12.2007" Time="22:01:05" DateTime="2007-12-16T21:01:05.578Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">do u want a php/mysql tutor ?</Text></Message><Message Date="16.12.2007" Time="22:02:14" DateTime="2007-12-16T21:02:14.593Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Well, not really a tutorial - I just want a screen capture of someone doing the project I posted, so that I can use that to learn from</Text></Message><Message Date="16.12.2007" Time="22:02:46" DateTime="2007-12-16T21:02:46.687Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">oh you have got 2 reviews on the same date.</Text></Message><Message Date="16.12.2007" Time="22:03:07" DateTime="2007-12-16T21:03:07.078Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">what is that xhtml/css project about ?</Text></Message><Message Date="16.12.2007" Time="22:03:11" DateTime="2007-12-16T21:03:11.234Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">is that the same work as mine ?</Text></Message><Message Date="16.12.2007" Time="22:03:55" DateTime="2007-12-16T21:03:55.453Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yes, someone else who coulnt manage to do the project in CSS - He used tables, I payed him out for someone else to do it</Text></Message><Message Date="16.12.2007" Time="22:03:59" DateTime="2007-12-16T21:03:59.546Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">And that was you :)</Text></Message><Message Date="16.12.2007" Time="22:04:44" DateTime="2007-12-16T21:04:44.750Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ha ha h a</Text></Message><Message Date="16.12.2007" Time="22:04:54" DateTime="2007-12-16T21:04:54.953Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">that's interesting </Text></Message><Message Date="16.12.2007" Time="22:05:05" DateTime="2007-12-16T21:05:05.171Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">and did he failed to do using css ?</Text></Message><Message Date="16.12.2007" Time="22:05:31" DateTime="2007-12-16T21:05:31.109Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Frankly, he didn't know much about css at all</Text></Message><Message Date="16.12.2007" Time="22:05:54" DateTime="2007-12-16T21:05:54.468Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">aha</Text></Message><Message Date="16.12.2007" Time="22:06:35" DateTime="2007-12-16T21:06:35.109Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">so u luckily got 2 reviews and one project done.</Text></Message><Message Date="16.12.2007" Time="22:06:35" DateTime="2007-12-16T21:06:35.125Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">that's good.</Text></Message><Message Date="16.12.2007" Time="22:07:33" DateTime="2007-12-16T21:07:33.859Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Hehe, yeah - but the reviews doesn't count quite as much as i'm a buyer</Text></Message><Message Date="16.12.2007" Time="22:07:59" DateTime="2007-12-16T21:07:59.234Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah the review will count.</Text></Message><Message Date="16.12.2007" Time="22:08:19" DateTime="2007-12-16T21:08:19.640Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">first i was in doubt whether to take the project or not.</Text></Message><Message Date="16.12.2007" Time="22:08:50" DateTime="2007-12-16T21:08:50.437Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">then i don't know what made me to bid on ur project.</Text></Message><Message Date="16.12.2007" Time="22:09:00" DateTime="2007-12-16T21:09:00.671Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but the moment i placed the bid u selected me.</Text></Message><Message Date="16.12.2007" Time="22:09:10" DateTime="2007-12-16T21:09:10.875Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">so why did u select me ?</Text></Message><Message Date="16.12.2007" Time="22:09:23" DateTime="2007-12-16T21:09:23.156Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Hehe, by looking at your reviews ;)</Text></Message><Message Date="16.12.2007" Time="22:09:38" DateTime="2007-12-16T21:09:38.390Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ha ha</Text></Message><Message Date="16.12.2007" Time="22:09:38" DateTime="2007-12-16T21:09:38.406Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">the same thing here</Text></Message><Message Date="16.12.2007" Time="22:09:58" DateTime="2007-12-16T21:09:58.812Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">good programmers will select only buyer with good reviews.</Text></Message><Message Date="16.12.2007" Time="22:09:58" DateTime="2007-12-16T21:09:58.828Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">do u know that.</Text></Message><Message Date="16.12.2007" Time="22:10:31" DateTime="2007-12-16T21:10:31.531Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">That's true :)</Text></Message><Message Date="16.12.2007" Time="22:11:06" DateTime="2007-12-16T21:11:06.937Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">so reviews always counts.</Text></Message><Message Date="16.12.2007" Time="22:11:17" DateTime="2007-12-16T21:11:17.140Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">and ur reviews are very good too.</Text></Message><Message Date="16.12.2007" Time="22:11:27" DateTime="2007-12-16T21:11:27.359Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">so next time it will be easy for you to hire a good programmer.</Text></Message><Message Date="16.12.2007" Time="22:11:47" DateTime="2007-12-16T21:11:47.765Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">if u need any visual php/mysql tutor i can provide u that.</Text></Message><Message Date="16.12.2007" Time="22:11:57" DateTime="2007-12-16T21:11:57.984Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">and it will cover all topics u wanted.</Text></Message><Message Date="16.12.2007" Time="22:12:08" DateTime="2007-12-16T21:12:08.406Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">if u need that pls let me know so i can help u.</Text></Message><Message Date="16.12.2007" Time="22:12:32" DateTime="2007-12-16T21:12:32.828Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Thanks, I'm gonna add something to that project though, so that I'm sure those I choose can manage it</Text></Message><Message Date="16.12.2007" Time="22:13:12" DateTime="2007-12-16T21:13:12.281Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">You know, I don't know what's possible and not in PHP/MySQL - the same way I didn't know about the functions in CSS</Text></Message><Message Date="16.12.2007" Time="22:14:48" DateTime="2007-12-16T21:14:48.812Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i can provide the tutors free for you.</Text></Message><Message Date="16.12.2007" Time="22:14:48" DateTime="2007-12-16T21:14:48.812Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i dont want any money</Text></Message><Message Date="16.12.2007" Time="22:14:48" DateTime="2007-12-16T21:14:48.812Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">take it free.</Text></Message><Message Date="16.12.2007" Time="22:14:48" DateTime="2007-12-16T21:14:48.828Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">if u want.</Text></Message><Message Date="16.12.2007" Time="22:16:30" DateTime="2007-12-16T21:16:30.125Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">For free? Might I ask, why would you want to do that?</Text></Message><Message Date="16.12.2007" Time="22:17:05" DateTime="2007-12-16T21:17:05.734Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">because i like u.</Text></Message><Message Date="16.12.2007" Time="22:18:55" DateTime="2007-12-16T21:18:55.546Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Wow.. I don't really know what to say</Text></Message><Message Date="16.12.2007" Time="22:19:33" DateTime="2007-12-16T21:19:33.531Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">But, maybe we can talk about this further tomorrow?</Text></Message><Message Date="16.12.2007" Time="22:19:48" DateTime="2007-12-16T21:19:48.937Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Message Date="16.12.2007" Time="22:19:48" DateTime="2007-12-16T21:19:48.953Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thank u</Text></Message><Message Date="16.12.2007" Time="22:19:48" DateTime="2007-12-16T21:19:48.953Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">see u later.</Text></Message><Message Date="16.12.2007" Time="22:19:58" DateTime="2007-12-16T21:19:58.093Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Thanks you, good night</Text></Message><Message Date="16.12.2007" Time="22:20:23" DateTime="2007-12-16T21:20:23.531Z" SessionID="6"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">good night.</Text></Message><Message Date="23.12.2007" Time="21:47:46" DateTime="2007-12-23T20:47:46.633Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hai</Text></Message><Message Date="23.12.2007" Time="21:47:46" DateTime="2007-12-23T20:47:46.711Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">r u there ?</Text></Message><Message Date="23.12.2007" Time="21:48:20" DateTime="2007-12-23T20:48:20.633Z" SessionID="7"><From><User FriendlyName="Julejørn"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Hi there, yea almost atleast - playing poker :P</Text></Message><Message Date="23.12.2007" Time="21:48:35" DateTime="2007-12-23T20:48:35.852Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">aha</Text></Message><Message Date="23.12.2007" Time="21:48:43" DateTime="2007-12-23T20:48:43.821Z" SessionID="7"><From><User FriendlyName="Julejørn"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">What's up?</Text></Message><Message Date="23.12.2007" Time="21:48:59" DateTime="2007-12-23T20:48:59.196Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">did u download the tutorial ?</Text></Message><Message Date="23.12.2007" Time="21:49:22" DateTime="2007-12-23T20:49:22.477Z" SessionID="7"><From><User FriendlyName="Julejørn"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Yea, the one from lynda, right?</Text></Message><Message Date="23.12.2007" Time="21:49:37" DateTime="2007-12-23T20:49:37.680Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah</Text></Message><Message Date="23.12.2007" Time="21:49:48" DateTime="2007-12-23T20:49:48.086Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">was that helpful ?</Text></Message><Message Date="23.12.2007" Time="21:51:18" DateTime="2007-12-23T20:51:18.946Z" SessionID="7"><From><User FriendlyName="Julejørn"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Well, I have the whole series one my computer - But I havent reached to watch it yet</Text></Message><Message Date="23.12.2007" Time="21:51:44" DateTime="2007-12-23T20:51:44.602Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">aha</Text></Message><Message Date="23.12.2007" Time="21:51:59" DateTime="2007-12-23T20:51:59.789Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">that's fine.</Text></Message><Message Date="23.12.2007" Time="21:52:19" DateTime="2007-12-23T20:52:19.571Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok I just want to ask u that.</Text></Message><Message Date="23.12.2007" Time="21:52:24" DateTime="2007-12-23T20:52:24.586Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">(Y)</Text></Message><Message Date="23.12.2007" Time="21:52:49" DateTime="2007-12-23T20:52:49.586Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">its 2.18 am here </Text></Message><Message Date="23.12.2007" Time="21:52:49" DateTime="2007-12-23T20:52:49.586Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">gonna get some sleep.</Text></Message><Message Date="23.12.2007" Time="21:52:56" DateTime="2007-12-23T20:52:56.586Z" SessionID="7"><From><User FriendlyName="Julejørn"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Thanks :) But the reason why I posted at SL is that I want to se something task-specific :)</Text></Message><Message Date="23.12.2007" Time="21:53:11" DateTime="2007-12-23T20:53:11.993Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok i got that.</Text></Message><Message Date="23.12.2007" Time="21:53:27" DateTime="2007-12-23T20:53:27.555Z" SessionID="7"><From><User FriendlyName="Julejørn"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Ah, good night then :)</Text></Message><Message Date="23.12.2007" Time="21:53:37" DateTime="2007-12-23T20:53:37.946Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">if u need any help let me know.</Text></Message><Message Date="23.12.2007" Time="21:53:37" DateTime="2007-12-23T20:53:37.961Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thanks.</Text></Message><Message Date="23.12.2007" Time="21:53:37" DateTime="2007-12-23T20:53:37.961Z" SessionID="7"><From><User FriendlyName="<EMAIL>"/></From><To><User FriendlyName="Julejørn"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">bye.</Text></Message><Message Date="23.12.2007" Time="21:53:40" DateTime="2007-12-23T20:53:40.618Z" SessionID="7"><From><User FriendlyName="Julejørn"/></From><To><User FriendlyName="<EMAIL>"/></To><Text Style="font-family:Microsoft Sans Serif; color:#000000; ">Bye</Text></Message></Log>

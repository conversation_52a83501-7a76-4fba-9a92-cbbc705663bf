<?xml version="1.0"?>
<?xml-stylesheet type='text/xsl' href='MessageLog.xsl'?>
<Log FirstSessionID="1" LastSessionID="12"><Message Date="27.10.2008" Time="17:41:08" DateTime="2008-10-27T16:41:08.835Z" SessionID="1"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hay whats new?</Text></Message><Message Date="27.10.2008" Time="17:41:22" DateTime="2008-10-27T16:41:22.866Z" SessionID="1"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Are you working on any new 3d movdels?</Text></Message><Message Date="05.11.2008" Time="18:25:02" DateTime="2008-11-05T17:25:02.578Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hey man, sorry for not replying, i've just been stressed out lately</Text></Message><Message Date="05.11.2008" Time="18:25:15" DateTime="2008-11-05T17:25:15.640Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">nothing up here, just work work work</Text></Message><Message Date="05.11.2008" Time="18:25:53" DateTime="2008-11-05T17:25:53.312Z" SessionID="2"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">havent even completed my ninja :/ what about you?</Text></Message><Message Date="05.11.2008" Time="18:35:34" DateTime="2008-11-05T17:35:34.203Z" SessionID="3"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text>as here work takes up all of my time.</Text></Message><Message Date="27.12.2008" Time="21:32:10" DateTime="2008-12-27T20:32:10.203Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hay how is it going??</Text></Message><Message Date="27.12.2008" Time="21:32:16" DateTime="2008-12-27T20:32:16.375Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey man</Text></Message><Message Date="27.12.2008" Time="21:32:32" DateTime="2008-12-27T20:32:32.468Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">just playing around with 3ds max, building some lego's ;)</Text></Message><Message Date="27.12.2008" Time="21:32:42" DateTime="2008-12-27T20:32:42.156Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what about you?</Text></Message><Message Date="27.12.2008" Time="21:32:54" DateTime="2008-12-27T20:32:54.875Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hehe cool i'm trying to build a game level in max.</Text></Message><Message Date="27.12.2008" Time="21:33:16" DateTime="2008-12-27T20:33:16.687Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool, anything to show yet?</Text></Message><Message Date="27.12.2008" Time="21:33:28" DateTime="2008-12-27T20:33:28.078Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">no only the refrence image.</Text></Message><Message Date="27.12.2008" Time="21:33:42" DateTime="2008-12-27T20:33:42.031Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">it is going to be something to do.</Text></Message><Message Date="27.12.2008" Time="21:33:46" DateTime="2008-12-27T20:33:46.609Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">:(</Text></Message><Message Date="27.12.2008" Time="21:34:13" DateTime="2008-12-27T20:34:13.437Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe, so is it actually going to be coded within a game?</Text></Message><Message Date="27.12.2008" Time="21:34:22" DateTime="2008-12-27T20:34:22.234Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah</Text></Message><Message Date="27.12.2008" Time="21:34:48" DateTime="2008-12-27T20:34:48.875Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i dont know if i can do it :(</Text></Message><Message Date="27.12.2008" Time="21:34:59" DateTime="2008-12-27T20:34:59.390Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what are the criterias?</Text></Message><Message Date="27.12.2008" Time="21:36:14" DateTime="2008-12-27T20:36:14.046Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">it needs to be lowpoly, but textures need to be detail. I also need to underground cave to be huge to hold all of the content.</Text></Message><Message Date="27.12.2008" Time="21:36:19" DateTime="2008-12-27T20:36:19.578Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">It is for a fps game</Text></Message><Message Date="27.12.2008" Time="21:36:52" DateTime="2008-12-27T20:36:52.984Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but is it with characters, or are you just creating the levels?</Text></Message><Message Date="27.12.2008" Time="21:37:36" DateTime="2008-12-27T20:37:36.937Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yes it will have monsters and other things in it.. i am just working on the level another person is working on the char.</Text></Message><Message Date="27.12.2008" Time="21:38:19" DateTime="2008-12-27T20:38:19.546Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so you need to create a cave in lowpoly? heh, are you good at texturing?</Text></Message><Message Date="27.12.2008" Time="21:38:31" DateTime="2008-12-27T20:38:31.609Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">Sorry if i asked you this already, but you use zbrush am i right?</Text></Message><Message Date="27.12.2008" Time="21:38:33" DateTime="2008-12-27T20:38:33.484Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah</Text></Message><Message Date="27.12.2008" Time="21:38:53" DateTime="2008-12-27T20:38:53.953Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i've just had a quick look at it</Text></Message><Message Date="27.12.2008" Time="21:39:01" DateTime="2008-12-27T20:39:01.515Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i don't know how to use it</Text></Message><Message Date="27.12.2008" Time="21:39:11" DateTime="2008-12-27T20:39:11.140Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ahh i see</Text></Message><Message Date="27.12.2008" Time="21:39:30" DateTime="2008-12-27T20:39:30.796Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">what is what i need to create within the cave.</Text></Message><Message Date="27.12.2008" Time="21:39:44" DateTime="2008-12-27T20:39:44.468Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">it is a huge complex</Text></Message><Message Date="27.12.2008" Time="21:40:11" DateTime="2008-12-27T20:40:11.656Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">I need it to look as high poly as i can get it for the game</Text></Message><Message Date="27.12.2008" Time="21:42:04" DateTime="2008-12-27T20:42:04.593Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it's amazing what you can do with good texturing.. i've seen some lowpoly models used in games, without textures they look very edgy and stuff, but when you see a render with textures it looks like it's high poly</Text></Message><Message Date="27.12.2008" Time="21:42:37" DateTime="2008-12-27T20:42:37.687Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but creating things in lowpoly is good training though</Text></Message><Message Date="27.12.2008" Time="21:42:46" DateTime="2008-12-27T20:42:46.828Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah because alot of them use normal maps</Text></Message><Message Date="27.12.2008" Time="21:42:55" DateTime="2008-12-27T20:42:55.890Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i am trying to do that in zbrush.</Text></Message><Message Date="27.12.2008" Time="21:43:36" DateTime="2008-12-27T20:43:36.640Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">are you creating the cave in zbrush? i thought zbrush generated some sick amount of poly's?</Text></Message><Message Date="27.12.2008" Time="21:44:05" DateTime="2008-12-27T20:44:05.546Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">I am trying to get some advice on how to model my cave in zbrush so i can create a really cool normal map for it.</Text></Message><Message Date="27.12.2008" Time="21:44:54" DateTime="2008-12-27T20:44:54.250Z" SessionID="4"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm no help on that one, sorry</Text></Message><Message Date="27.12.2008" Time="21:45:05" DateTime="2008-12-27T20:45:05.906Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">I dont know alot of ppl who i can chat with about it.</Text></Message><Message Date="27.12.2008" Time="21:45:10" DateTime="2008-12-27T20:45:10.687Z" SessionID="4"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thats ok.</Text></Message><Message Date="27.12.2008" Time="21:47:45" DateTime="2008-12-27T20:47:45.171Z" SessionID="5"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">So what are you making out of the lego blocks in max?</Text></Message><Message Date="27.12.2008" Time="21:48:31" DateTime="2008-12-27T20:48:31.031Z" SessionID="5"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm just creating the different lego blocks now, havent figured out what to do with them, but i guess i may come up with something good :)</Text></Message><Message Date="27.12.2008" Time="21:50:04" DateTime="2008-12-27T20:50:04.171Z" SessionID="5"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">cool</Text></Message><Message Date="27.12.2008" Time="23:09:36" DateTime="2008-12-27T22:09:36.953Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">have you ever used nurbs for modeling?</Text></Message><Message Date="27.12.2008" Time="23:10:02" DateTime="2008-12-27T22:10:02.640Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">a few times.</Text></Message><Message Date="27.12.2008" Time="23:10:38" DateTime="2008-12-27T22:10:38.968Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm trying out to make a pretty simple model with splines and U loft</Text></Message><Message Date="27.12.2008" Time="23:10:52" DateTime="2008-12-27T22:10:52.578Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but i'm not sure how to deal with the arms</Text></Message><Message Date="27.12.2008" Time="23:11:02" DateTime="2008-12-27T22:11:02.390Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">from the body to the arms</Text></Message><Message Date="27.12.2008" Time="23:11:37" DateTime="2008-12-27T22:11:37.203Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">are you trying to model a human models with nurbs?</Text></Message><Message Date="27.12.2008" Time="23:12:04" DateTime="2008-12-27T22:12:04.796Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it's a penguin, pretty simple model, i can send you an image</Text></Message><Message Date="27.12.2008" Time="23:12:26" DateTime="2008-12-27T22:12:26.484Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">please do</Text></Message><Message Date="27.12.2008" Time="23:12:31" DateTime="2008-12-27T22:12:31.578Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but i would not use nurbs</Text></Message><Invitation Date="27.12.2008" Time="23:13:09" DateTime="2008-12-27T22:13:09.375Z" SessionID="6"><From><User FriendlyName="Jh"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\peng.jpg</File><Text Style="color:#545454; ">Jh sender C:\Documents and Settings\Administrator\Skrivebord\peng.jpg</Text></Invitation><InvitationResponse Date="27.12.2008" Time="23:13:30" DateTime="2008-12-27T22:13:30.156Z" SessionID="6"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\peng.jpg</File><Text Style="color:#545454; ">Overføring av "peng.jpg" er fullført.</Text></InvitationResponse><Message Date="27.12.2008" Time="23:14:56" DateTime="2008-12-27T22:14:56.812Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hmmm</Text></Message><Message Date="27.12.2008" Time="23:15:55" DateTime="2008-12-27T22:15:55.312Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">are you good with created models in max?</Text></Message><Message Date="27.12.2008" Time="23:16:10" DateTime="2008-12-27T22:16:10.406Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">sorry i cant rember if you show me any of your 3d modesl</Text></Message><Message Date="27.12.2008" Time="23:16:15" DateTime="2008-12-27T22:16:15.468Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">models</Text></Message><Message Date="27.12.2008" Time="23:17:56" DateTime="2008-12-27T22:17:56.671Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nah, not really.. i've spendt most time on animating, played around with fluids etc</Text></Message><Message Date="27.12.2008" Time="23:18:32" DateTime="2008-12-27T22:18:32.625Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">my only complete character modelled was from one of the 3dbuzz's tutorials</Text></Message><Message Date="27.12.2008" Time="23:18:36" DateTime="2008-12-27T22:18:36.265Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">an alien model</Text></Message><Message Date="27.12.2008" Time="23:18:57" DateTime="2008-12-27T22:18:57.812Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ahh i see.</Text></Message><Message Date="27.12.2008" Time="23:18:58" DateTime="2008-12-27T22:18:58.515Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that was box-modeling</Text></Message><Message Date="27.12.2008" Time="23:19:21" DateTime="2008-12-27T22:19:21.375Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">I would use this same tihng for this model you are trying to create.</Text></Message><Message Date="27.12.2008" Time="23:19:31" DateTime="2008-12-27T22:19:31.296Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i would not use nurbs</Text></Message><Message Date="27.12.2008" Time="23:20:14" DateTime="2008-12-27T22:20:14.312Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i've seen a nurbs tutorial some time ago, looked pretty simple with the lofting, but i can't remember having seen the arm part in the tutorial.. so i guess you're right</Text></Message><Message Date="27.12.2008" Time="23:21:10" DateTime="2008-12-27T22:21:10.343Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">do you still have a link to that tut i would like to check it out.</Text></Message><Message Date="27.12.2008" Time="23:21:26" DateTime="2008-12-27T22:21:26.765Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">which one, the nurbs tutorial or the alien?</Text></Message><Message Date="27.12.2008" Time="23:21:39" DateTime="2008-12-27T22:21:39.843Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">nurbs</Text></Message><Message Date="27.12.2008" Time="23:21:51" DateTime="2008-12-27T22:21:51.125Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the nurbs tutorial is long gone, i think it's two years or so since i've seen it</Text></Message><Message Date="27.12.2008" Time="23:22:02" DateTime="2008-12-27T22:22:02.250Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">oh ok</Text></Message><Message Date="27.12.2008" Time="23:22:47" DateTime="2008-12-27T22:22:47.750Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm just searching around, and it seems nurbs are usually used on objects like cars/phones etc</Text></Message><Message Date="27.12.2008" Time="23:24:31" DateTime="2008-12-27T22:24:31.828Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah, but i dont really use nurbs much i like poly modelinh.</Text></Message><Message Date="27.12.2008" Time="23:26:00" DateTime="2008-12-27T22:26:00.390Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">do you draw alot??</Text></Message><Message Date="27.12.2008" Time="23:28:14" DateTime="2008-12-27T22:28:14.640Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">naah, i can't find the time for it anymore</Text></Message><Message Date="27.12.2008" Time="23:28:24" DateTime="2008-12-27T22:28:24.468Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">do you have a side vide to that pic?</Text></Message><Invitation Date="27.12.2008" Time="23:29:58" DateTime="2008-12-27T22:29:58.296Z" SessionID="6"><From><User FriendlyName="Jh"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\side.jpg</File><Text Style="color:#545454; ">Jh sender C:\Documents and Settings\Administrator\Skrivebord\side.jpg</Text></Invitation><Message Date="27.12.2008" Time="23:30:01" DateTime="2008-12-27T22:30:01.093Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">this isn't my drawing, this an image i found on the web</Text></Message><InvitationResponse Date="27.12.2008" Time="23:30:09" DateTime="2008-12-27T22:30:09.796Z" SessionID="6"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\side.jpg</File><Text Style="color:#545454; ">Overføring av "side.jpg" er fullført.</Text></InvitationResponse><Message Date="27.12.2008" Time="23:30:13" DateTime="2008-12-27T22:30:13.531Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so that's the closest one i found'</Text></Message><Message Date="27.12.2008" Time="23:30:23" DateTime="2008-12-27T22:30:23.437Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">oh ok</Text></Message><Message Date="27.12.2008" Time="23:30:48" DateTime="2008-12-27T22:30:48.625Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah this looks really simple to do.</Text></Message><Message Date="27.12.2008" Time="23:32:09" DateTime="2008-12-27T22:32:09.484Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah it's pretty basic huh, so i got started (box modeling), after spending ten minutes on the wings i jumped on to nurbs :P</Text></Message><Message Date="27.12.2008" Time="23:32:28" DateTime="2008-12-27T22:32:28.500Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">lol</Text></Message><Message Date="27.12.2008" Time="23:32:47" DateTime="2008-12-27T22:32:47.593Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">did you save the box model?</Text></Message><Message Date="27.12.2008" Time="23:32:59" DateTime="2008-12-27T22:32:59.625Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">if you are having problems maybe i can help with it.</Text></Message><Message Date="27.12.2008" Time="23:33:28" DateTime="2008-12-27T22:33:28.437Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i discarded it :( i'd appreciate some help :)</Text></Message><Message Date="27.12.2008" Time="23:33:59" DateTime="2008-12-27T22:33:59.093Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hehe thats one thing you should not do. Never deleate any models you start</Text></Message><Message Date="27.12.2008" Time="23:34:02" DateTime="2008-12-27T22:34:02.578Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">first question, when you're modeling, do you start of with the shape from the front-view, or do you do all angels at the same time?</Text></Message><Message Date="27.12.2008" Time="23:34:27" DateTime="2008-12-27T22:34:27.781Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">even if you plan to model it a diffrent way always save the first atempt</Text></Message><Message Date="27.12.2008" Time="23:34:35" DateTime="2008-12-27T22:34:35.593Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">you never know if you may have need for it.</Text></Message><Message Date="27.12.2008" Time="23:34:36" DateTime="2008-12-27T22:34:36.000Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">heh, it wasn't much to keep :P</Text></Message><Message Date="27.12.2008" Time="23:34:47" DateTime="2008-12-27T22:34:47.140Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but i see your point</Text></Message><Message Date="27.12.2008" Time="23:35:59" DateTime="2008-12-27T22:35:59.156Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">cool well if you get started on it again with box modeling send me some reander of the parts you are having problems with i will be happy to give you some pointes on how to fix it if i can.</Text></Message><Message Date="27.12.2008" Time="23:36:10" DateTime="2008-12-27T22:36:10.812Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">great, thanks</Text></Message><Message Date="27.12.2008" Time="23:36:26" DateTime="2008-12-27T22:36:26.500Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but the question</Text></Message><Message Date="27.12.2008" Time="23:36:32" DateTime="2008-12-27T22:36:32.781Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yes?</Text></Message><Message Date="27.12.2008" Time="23:36:34" DateTime="2008-12-27T22:36:34.156Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">when you're modeling, do you start of with the shape from the front-view, or do you do all angels at the same time?</Text></Message><Message Date="27.12.2008" Time="23:37:50" DateTime="2008-12-27T22:37:50.265Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">all the angles.. If you only do one angle you spend alot of time trying to fix the other side. you are createing a 3d models so it is best to work on it as such.</Text></Message><Message Date="27.12.2008" Time="23:38:18" DateTime="2008-12-27T22:38:18.531Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">That way you have a really good idea on how the model is taking shape.</Text></Message><Message Date="27.12.2008" Time="23:38:33" DateTime="2008-12-27T22:38:33.921Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">makes sense.. alright, i'll get started :)</Text></Message><Message Date="27.12.2008" Time="23:38:53" DateTime="2008-12-27T22:38:53.359Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">cool</Text></Message><Message Date="27.12.2008" Time="23:39:16" DateTime="2008-12-27T22:39:16.906Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">well i am around, because i'm trying to work on this game level so if you have any question just msg me.</Text></Message><Message Date="27.12.2008" Time="23:39:33" DateTime="2008-12-27T22:39:33.468Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i will :)</Text></Message><Message Date="28.12.2008" Time="00:59:43" DateTime="2008-12-27T23:59:43.125Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how are your game level going?</Text></Message><Message Date="28.12.2008" Time="01:00:09" DateTime="2008-12-28T00:00:09.093Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">slow watching stuff on youtube LOL!!</Text></Message><Message Date="28.12.2008" Time="01:00:17" DateTime="2008-12-28T00:00:17.171Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">haha</Text></Message><Message Date="28.12.2008" Time="01:01:38" DateTime="2008-12-28T00:01:38.906Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah</Text></Message><Message Date="28.12.2008" Time="01:04:04" DateTime="2008-12-28T00:04:04.453Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">how is the model coming along??</Text></Message><Message Date="28.12.2008" Time="01:04:26" DateTime="2008-12-28T00:04:26.578Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">lol</Text></Message><Invitation Date="28.12.2008" Time="01:04:41" DateTime="2008-12-28T00:04:41.984Z" SessionID="6"><From><User FriendlyName="Jh"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\peng.3DS</File><Text Style="color:#545454; ">Jh sender C:\Documents and Settings\Administrator\Skrivebord\peng.3DS</Text></Invitation><Message Date="28.12.2008" Time="01:05:01" DateTime="2008-12-28T00:05:01.328Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not much</Text></Message><Message Date="28.12.2008" Time="01:05:10" DateTime="2008-12-28T00:05:10.750Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">what verison of max do you have??</Text></Message><Message Date="28.12.2008" Time="01:05:15" DateTime="2008-12-28T00:05:15.875Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">9</Text></Message><Message Date="28.12.2008" Time="01:05:25" DateTime="2008-12-28T00:05:25.765Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">2009 0r just 9?</Text></Message><Message Date="28.12.2008" Time="01:05:29" DateTime="2008-12-28T00:05:29.359Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">just 9</Text></Message><Message Date="28.12.2008" Time="01:05:44" DateTime="2008-12-28T00:05:44.359Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">export it as a .obj file</Text></Message><Message Date="28.12.2008" Time="01:05:48" DateTime="2008-12-28T00:05:48.796Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">mkay</Text></Message><InvitationResponse Date="28.12.2008" Time="01:05:56" DateTime="2008-12-28T00:05:56.671Z" SessionID="6"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\peng.3DS</File><Text Style="color:#800000; ">Kan ikke sende "peng.3DS" til Fox.</Text></InvitationResponse><Invitation Date="28.12.2008" Time="01:06:17" DateTime="2008-12-28T00:06:17.687Z" SessionID="6"><From><User FriendlyName="Jh"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\peng.obj</File><Text Style="color:#545454; ">Jh sender C:\Documents and Settings\Administrator\Skrivebord\peng.obj</Text></Invitation><InvitationResponse Date="28.12.2008" Time="01:06:53" DateTime="2008-12-28T00:06:53.687Z" SessionID="6"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\peng.obj</File><Text Style="color:#545454; ">Overføring av "peng.obj" er fullført.</Text></InvitationResponse><Message Date="28.12.2008" Time="01:10:09" DateTime="2008-12-28T00:10:09.687Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">not bad it is shaping up</Text></Message><Message Date="28.12.2008" Time="01:11:29" DateTime="2008-12-28T00:11:29.359Z" SessionID="6"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i think that's it for today</Text></Message><Message Date="28.12.2008" Time="01:11:41" DateTime="2008-12-28T00:11:41.859Z" SessionID="6"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok</Text></Message><Message Date="28.12.2008" Time="17:08:49" DateTime="2008-12-28T16:08:49.546Z" SessionID="7"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how are your game level going?</Text></Message><Message Date="28.12.2008" Time="17:13:28" DateTime="2008-12-28T16:13:28.093Z" SessionID="8"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">t d</Text></Message><Message Date="28.12.2008" Time="17:13:39" DateTime="2008-12-28T16:13:39.218Z" SessionID="9"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">not good</Text></Message><Message Date="28.12.2008" Time="17:14:07" DateTime="2008-12-28T16:14:07.031Z" SessionID="9"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">can i have a look?</Text></Message><Message Date="28.12.2008" Time="17:30:24" DateTime="2008-12-28T16:30:24.562Z" SessionID="9"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">sorry  was geting smthint eat</Text></Message><Message Date="28.12.2008" Time="17:31:17" DateTime="2008-12-28T16:31:17.750Z" SessionID="9"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i dnt have much to look at</Text></Message><Message Date="28.12.2008" Time="17:31:24" DateTime="2008-12-28T16:31:24.546Z" SessionID="9"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">mkay</Text></Message><Message Date="28.12.2008" Time="17:34:29" DateTime="2008-12-28T16:34:29.625Z" SessionID="9"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">how is your project coming along?</Text></Message><Message Date="28.12.2008" Time="17:37:34" DateTime="2008-12-28T16:37:34.234Z" SessionID="9"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">starting to look like something</Text></Message><Invitation Date="28.12.2008" Time="17:37:41" DateTime="2008-12-28T16:37:41.156Z" SessionID="9"><From><User FriendlyName="Jh"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\1.jpg</File><Text Style="color:#545454; ">Jh sender C:\Documents and Settings\Administrator\Skrivebord\1.jpg</Text></Invitation><Invitation Date="28.12.2008" Time="17:37:47" DateTime="2008-12-28T16:37:47.781Z" SessionID="9"><From><User FriendlyName="Jh"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\2.jpg</File><Text Style="color:#545454; ">Jh sender C:\Documents and Settings\Administrator\Skrivebord\2.jpg</Text></Invitation><InvitationResponse Date="28.12.2008" Time="17:38:05" DateTime="2008-12-28T16:38:05.656Z" SessionID="9"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\2.jpg</File><Text Style="color:#545454; ">Overføring av "2.jpg" er fullført.</Text></InvitationResponse><InvitationResponse Date="28.12.2008" Time="17:38:08" DateTime="2008-12-28T16:38:08.062Z" SessionID="9"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\1.jpg</File><Text Style="color:#545454; ">Overføring av "1.jpg" er fullført.</Text></InvitationResponse><Message Date="28.12.2008" Time="17:38:42" DateTime="2008-12-28T16:38:42.796Z" SessionID="10"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">cool</Text></Message><Message Date="11.01.2009" Time="23:42:59" DateTime="2009-01-11T22:42:59.171Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hi how are you??</Text></Message><Message Date="11.01.2009" Time="23:44:00" DateTime="2009-01-11T22:44:00.671Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hola</Text></Message><Message Date="11.01.2009" Time="23:44:04" DateTime="2009-01-11T22:44:04.140Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm good</Text></Message><Message Date="11.01.2009" Time="23:44:07" DateTime="2009-01-11T22:44:07.171Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and you?</Text></Message><Message Date="11.01.2009" Time="23:44:24" DateTime="2009-01-11T22:44:24.890Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">I'm ok just chilling at home trying to find something to model in max.</Text></Message><Message Date="11.01.2009" Time="23:45:51" DateTime="2009-01-11T22:45:51.468Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">heh, if you're bored you could try do model the hands on my ninja character :P</Text></Message><Message Date="11.01.2009" Time="23:46:19" DateTime="2009-01-11T22:46:19.312Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hehe</Text></Message><Message Date="11.01.2009" Time="23:46:47" DateTime="2009-01-11T22:46:47.468Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">That sounds cool</Text></Message><Message Date="11.01.2009" Time="23:46:55" DateTime="2009-01-11T22:46:55.859Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">can i see what you have of your ninja??</Text></Message><Invitation Date="11.01.2009" Time="23:47:13" DateTime="2009-01-11T22:47:13.062Z" SessionID="11"><From><User FriendlyName="Jh"/></From><File>D:\Privat\Dokumenter\3D\3d prosjekter\ninja\test14.max</File><Text Style="color:#545454; ">Jh sender D:\Privat\Dokumenter\3D\3d prosjekter\ninja\test14.max</Text></Invitation><Message Date="11.01.2009" Time="23:47:28" DateTime="2009-01-11T22:47:28.015Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">is that max 2009?</Text></Message><Message Date="11.01.2009" Time="23:47:44" DateTime="2009-01-11T22:47:44.703Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nope, max v 9, 2007 or 2008 i think</Text></Message><Message Date="11.01.2009" Time="23:48:23" DateTime="2009-01-11T22:48:23.625Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">oh it will not open for me you need to export it as an obj but remove any mesh smooth ot rurbo from it befor you export it.</Text></Message><Message Date="11.01.2009" Time="23:48:36" DateTime="2009-01-11T22:48:36.515Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">2 sec</Text></Message><Invitation Date="11.01.2009" Time="23:51:41" DateTime="2009-01-11T22:51:41.750Z" SessionID="11"><From><User FriendlyName="Jh"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\ninja.jpg</File><Text Style="color:#545454; ">Jh sender C:\Documents and Settings\Administrator\Skrivebord\ninja.jpg</Text></Invitation><Message Date="11.01.2009" Time="23:51:45" DateTime="2009-01-11T22:51:45.265Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">do you know anything about max scrips??</Text></Message><InvitationResponse Date="11.01.2009" Time="23:51:48" DateTime="2009-01-11T22:51:48.093Z" SessionID="11"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\ninja.jpg</File><Text Style="color:#545454; ">Overføring av "ninja.jpg" er fullført.</Text></InvitationResponse><Invitation Date="11.01.2009" Time="23:51:49" DateTime="2009-01-11T22:51:49.546Z" SessionID="11"><From><User FriendlyName="Jh"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\ninja.obj</File><Text Style="color:#545454; ">Jh sender C:\Documents and Settings\Administrator\Skrivebord\ninja.obj</Text></Invitation><InvitationResponse Date="11.01.2009" Time="23:52:03" DateTime="2009-01-11T22:52:03.812Z" SessionID="11"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\ninja.obj</File><Text Style="color:#545454; ">Overføring av "ninja.obj" er fullført.</Text></InvitationResponse><Message Date="11.01.2009" Time="23:52:07" DateTime="2009-01-11T22:52:07.718Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nothing at all, i only know some of it's uses</Text></Message><Message Date="11.01.2009" Time="23:52:45" DateTime="2009-01-11T22:52:45.875Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ahh i'm trying to learn more about it it is really cool to help you speed up work on your projects.</Text></Message><Message Date="11.01.2009" Time="23:53:21" DateTime="2009-01-11T22:53:21.734Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">is it hard to learn?</Text></Message><Message Date="11.01.2009" Time="23:53:50" DateTime="2009-01-11T22:53:50.500Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">kinda</Text></Message><Message Date="11.01.2009" Time="23:54:31" DateTime="2009-01-11T22:54:31.375Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">is it possible to compare it with c++ e.g?</Text></Message><Message Date="11.01.2009" Time="23:55:06" DateTime="2009-01-11T22:55:06.781Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">a little.</Text></Message><Message Date="12.01.2009" Time="00:01:13" DateTime="2009-01-11T23:01:13.484Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">nice ninga</Text></Message><Message Date="12.01.2009" Time="00:01:19" DateTime="2009-01-11T23:01:19.453Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ninja</Text></Message><Message Date="12.01.2009" Time="00:01:59" DateTime="2009-01-11T23:01:59.859Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks, i had some problems with the head a while ago, when you remote controled me to get it right.. it was just the surbs subvision thingy :P</Text></Message><Message Date="12.01.2009" Time="00:02:38" DateTime="2009-01-11T23:02:38.046Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ahh i see</Text></Message><Message Date="12.01.2009" Time="00:28:37" DateTime="2009-01-11T23:28:37.312Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">do you have plans to rig this mode;?</Text></Message><Message Date="12.01.2009" Time="00:28:51" DateTime="2009-01-11T23:28:51.781Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yep</Text></Message><Message Date="12.01.2009" Time="00:29:21" DateTime="2009-01-11T23:29:21.453Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but i think i need to recreate it, because i don't think it's very good for modeling atm</Text></Message><Message Date="12.01.2009" Time="00:29:47" DateTime="2009-01-11T23:29:47.921Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">how detailed are you trying to get it to look?</Text></Message><Message Date="12.01.2009" Time="00:30:07" DateTime="2009-01-11T23:30:07.265Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">is it a cartoon ninja you are shooting for??</Text></Message><Message Date="12.01.2009" Time="00:30:29" DateTime="2009-01-11T23:30:29.796Z" SessionID="11"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not too detailed, not cartoon, but close</Text></Message><Message Date="12.01.2009" Time="00:30:46" DateTime="2009-01-11T23:30:46.375Z" SessionID="11"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ahh i see.</Text></Message><Message Date="18.01.2009" Time="19:25:36" DateTime="2009-01-18T18:25:36.796Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hay how is it going?</Text></Message><Message Date="18.01.2009" Time="19:30:03" DateTime="2009-01-18T18:30:03.375Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey</Text></Message><Message Date="18.01.2009" Time="19:30:19" DateTime="2009-01-18T18:30:19.078Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm trying to skin my character atm</Text></Message><Message Date="18.01.2009" Time="19:30:27" DateTime="2009-01-18T18:30:27.437Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">having some issues with the upper legs</Text></Message><Message Date="18.01.2009" Time="19:30:45" DateTime="2009-01-18T18:30:45.031Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah that is always a problems part to skin</Text></Message><Message Date="18.01.2009" Time="19:31:06" DateTime="2009-01-18T18:31:06.687Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure is</Text></Message><Message Date="18.01.2009" Time="19:31:18" DateTime="2009-01-18T18:31:18.703Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">whaT ABOUT YOU? WHAT ARE YOU UP TO?</Text></Message><Message Date="18.01.2009" Time="19:31:22" DateTime="2009-01-18T18:31:22.500Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oops, caps</Text></Message><Message Date="18.01.2009" Time="19:31:37" DateTime="2009-01-18T18:31:37.546Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">trying to work on my female 3d model</Text></Message><Message Date="18.01.2009" Time="19:32:15" DateTime="2009-01-18T18:32:15.203Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how far have you come?</Text></Message><Message Date="18.01.2009" Time="19:32:44" DateTime="2009-01-18T18:32:44.656Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">slow</Text></Message><Message Date="18.01.2009" Time="19:32:48" DateTime="2009-01-18T18:32:48.125Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">http://genzoman.deviantart.com/art/katara-58925762</Text></Message><Message Date="18.01.2009" Time="19:32:56" DateTime="2009-01-18T18:32:56.171Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i am plaing to model her</Text></Message><Message Date="18.01.2009" Time="19:33:37" DateTime="2009-01-18T18:33:37.546Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="18.01.2009" Time="19:33:49" DateTime="2009-01-18T18:33:49.625Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">are you starting on the head, or the legs?</Text></Message><Message Date="18.01.2009" Time="19:34:01" DateTime="2009-01-18T18:34:01.484Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i am starting on the body first</Text></Message><Message Date="18.01.2009" Time="19:34:11" DateTime="2009-01-18T18:34:11.875Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">have you got reference images to work with?</Text></Message><Message Date="18.01.2009" Time="19:34:22" DateTime="2009-01-18T18:34:22.578Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i also plan to do the girl she hangs out with.</Text></Message><Message Date="18.01.2009" Time="19:34:49" DateTime="2009-01-18T18:34:49.875Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i dont know if you know the show avatar the last airbender</Text></Message><Message Date="18.01.2009" Time="19:36:06" DateTime="2009-01-18T18:36:06.562Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">this?
http://fc03.deviantart.com/fs18/f/2007/183/a/c/Avatar__The_Last_Airbender_by_finni.jpg</Text></Message><Message Date="18.01.2009" Time="19:37:21" DateTime="2009-01-18T18:37:21.156Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah</Text></Message><Message Date="18.01.2009" Time="19:37:31" DateTime="2009-01-18T18:37:31.390Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i plan the do the girl in yellow and red also</Text></Message><Message Date="18.01.2009" Time="19:37:41" DateTime="2009-01-18T18:37:41.468Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but look more like the drawing i send you.</Text></Message><Message Date="18.01.2009" Time="19:37:42" DateTime="2009-01-18T18:37:42.906Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">wow, large project</Text></Message><Message Date="18.01.2009" Time="19:37:57" DateTime="2009-01-18T18:37:57.390Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah</Text></Message><Message Date="18.01.2009" Time="19:41:27" DateTime="2009-01-18T18:41:27.625Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">what are you using to rig your ninja??</Text></Message><Message Date="18.01.2009" Time="19:41:46" DateTime="2009-01-18T18:41:46.359Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">bones</Text></Message><Message Date="18.01.2009" Time="19:41:59" DateTime="2009-01-18T18:41:59.359Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">then i use motionbuilder for animating</Text></Message><Message Date="18.01.2009" Time="19:42:06" DateTime="2009-01-18T18:42:06.578Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it rigs it automaticly</Text></Message><Message Date="18.01.2009" Time="19:42:51" DateTime="2009-01-18T18:42:51.328Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">when you've named your bones correctly, motionbuilder use a "characterize" tool, that does the whole rigging jo automaticly</Text></Message><Message Date="18.01.2009" Time="19:43:24" DateTime="2009-01-18T18:43:24.093Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">really i never knew that..</Text></Message><Message Date="18.01.2009" Time="19:43:39" DateTime="2009-01-18T18:43:39.000Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">dude, it's sick.. you should check it out</Text></Message><Message Date="18.01.2009" Time="19:43:40" DateTime="2009-01-18T18:43:40.187Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">the sure would have saved me alot of time in creating my own costom rig</Text></Message><Message Date="18.01.2009" Time="19:43:52" DateTime="2009-01-18T18:43:52.625Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i have a copy of it, but i dont use it.</Text></Message><Message Date="18.01.2009" Time="19:43:58" DateTime="2009-01-18T18:43:58.921Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it's really cool to create good rigs, but you'd be amazed if you saw it</Text></Message><Message Date="18.01.2009" Time="19:44:36" DateTime="2009-01-18T18:44:36.578Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">3d buzz have several hours of videotutorials on it</Text></Message><Message Date="18.01.2009" Time="19:45:51" DateTime="2009-01-18T18:45:51.234Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">sweet i will need to take a look at it.</Text></Message><Message Date="18.01.2009" Time="19:46:36" DateTime="2009-01-18T18:46:36.625Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you can do really much with it, but just the part where you characterize your character is incredibly easy, it's one of the first tutorials</Text></Message><Message Date="18.01.2009" Time="19:47:05" DateTime="2009-01-18T18:47:05.812Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you can save poses, blend animation together, re-use animations etc</Text></Message><Message Date="18.01.2009" Time="19:47:18" DateTime="2009-01-18T18:47:18.281Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">then there's a lot of tools just to do the animating</Text></Message><Message Date="18.01.2009" Time="19:47:42" DateTime="2009-01-18T18:47:42.781Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i relly need to look into it some more.</Text></Message><Message Date="18.01.2009" Time="19:51:36" DateTime="2009-01-18T18:51:36.781Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">my rig is realy cool, because i have alot of control with it, but the skinning is not easy to do so my model cant move untill i complete the skinning.</Text></Message><Message Date="18.01.2009" Time="19:53:14" DateTime="2009-01-18T18:53:14.515Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">do you use polyboost?</Text></Message><Message Date="18.01.2009" Time="19:53:33" DateTime="2009-01-18T18:53:33.546Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">never heard about it, what is it?</Text></Message><Message Date="18.01.2009" Time="19:53:55" DateTime="2009-01-18T18:53:55.312Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">you should check it out you will like what you can do ot help speed up your modeling.</Text></Message><Message Date="18.01.2009" Time="19:54:41" DateTime="2009-01-18T18:54:41.296Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">http://www.polyboost.com/</Text></Message><Message Date="18.01.2009" Time="19:54:45" DateTime="2009-01-18T18:54:45.125Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">check out the videos</Text></Message><Message Date="18.01.2009" Time="19:55:10" DateTime="2009-01-18T18:55:10.890Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks, i'll check it out :)</Text></Message><Message Date="18.01.2009" Time="19:55:22" DateTime="2009-01-18T18:55:22.343Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">here's a video from motionbuilder
http://www.3dbuzz.com/vbforum/simplevideo.php?v=1098&amp;t=1.5</Text></Message><Message Date="18.01.2009" Time="19:59:50" DateTime="2009-01-18T18:59:50.593Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hmm it is not loasing up</Text></Message><Message Date="18.01.2009" Time="20:01:35" DateTime="2009-01-18T19:01:35.640Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hmm, may be that you need to login</Text></Message><Message Date="18.01.2009" Time="20:01:39" DateTime="2009-01-18T19:01:39.250Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i can send it to you</Text></Message><Invitation Date="18.01.2009" Time="20:01:45" DateTime="2009-01-18T19:01:45.625Z" SessionID="12"><From><User FriendlyName="Jh"/></From><File>D:\Video Tutorials\(3D Buzz) Motionbuilder\Issue 1\1- Introduction.avi</File><Text Style="color:#545454; ">Jh sender D:\Video Tutorials\(3D Buzz) Motionbuilder\Issue 1\1- Introduction.avi</Text></Invitation><Message Date="18.01.2009" Time="20:04:35" DateTime="2009-01-18T19:04:35.031Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">cool thanks</Text></Message><Message Date="18.01.2009" Time="20:04:37" DateTime="2009-01-18T19:04:37.625Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">damn, polyboost looks sweet</Text></Message><Message Date="18.01.2009" Time="20:04:43" DateTime="2009-01-18T19:04:43.203Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">it is</Text></Message><Message Date="18.01.2009" Time="20:04:49" DateTime="2009-01-18T19:04:49.968Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i have it and i like what i can do with it.</Text></Message><Message Date="18.01.2009" Time="20:08:37" DateTime="2009-01-18T19:08:37.703Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">by the way, remember the tutorial i sendt you about skinning</Text></Message><Message Date="18.01.2009" Time="20:08:50" DateTime="2009-01-18T19:08:50.875Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">they use motionbuilder at the end of that video</Text></Message><Message Date="18.01.2009" Time="20:10:26" DateTime="2009-01-18T19:10:26.203Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">the tut you just send me?</Text></Message><Message Date="18.01.2009" Time="20:10:58" DateTime="2009-01-18T19:10:58.875Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">a couple of months ago</Text></Message><Message Date="18.01.2009" Time="20:11:05" DateTime="2009-01-18T19:11:05.625Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">videotutorial about skinning</Text></Message><Message Date="18.01.2009" Time="20:11:57" DateTime="2009-01-18T19:11:57.234Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ummm i cant semm to rember it</Text></Message><Message Date="18.01.2009" Time="20:12:04" DateTime="2009-01-18T19:12:04.765Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">or i loast it when my pc crashed</Text></Message><Message Date="18.01.2009" Time="20:12:41" DateTime="2009-01-18T19:12:41.265Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i can resend it if you want</Text></Message><InvitationResponse Date="18.01.2009" Time="20:12:47" DateTime="2009-01-18T19:12:47.625Z" SessionID="12"><From><User FriendlyName="Fox"/></From><File>D:\Video Tutorials\(3D Buzz) Motionbuilder\Issue 1\1- Introduction.avi</File><Text Style="color:#545454; ">Overføring av "1- Introduction.avi" er fullført.</Text></InvitationResponse><Message Date="18.01.2009" Time="20:42:43" DateTime="2009-01-18T19:42:43.953Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">that was a really good preview on it.. i cant wait to see how your rig will turn out.</Text></Message><Message Date="18.01.2009" Time="20:43:36" DateTime="2009-01-18T19:43:36.828Z" SessionID="12"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, if i just could skin it properly :P</Text></Message><Message Date="18.01.2009" Time="20:44:00" DateTime="2009-01-18T19:44:00.640Z" SessionID="12"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hehe yeah i know what you mean.</Text></Message><Message Date="24.01.2009" Time="19:06:19" DateTime="2009-01-24T18:06:19.406Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">how goes your skinning and rigging??</Text></Message><Message Date="24.01.2009" Time="19:06:34" DateTime="2009-01-24T18:06:34.171Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, i've taken a break from it</Text></Message><Message Date="24.01.2009" Time="19:06:37" DateTime="2009-01-24T18:06:37.265Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">drives me crazy</Text></Message><Message Date="24.01.2009" Time="19:06:54" DateTime="2009-01-24T18:06:54.375Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hehehe i know that feeling.</Text></Message><Message Date="24.01.2009" Time="19:07:08" DateTime="2009-01-24T18:07:08.718Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah :P</Text></Message><Message Date="24.01.2009" Time="19:07:53" DateTime="2009-01-24T18:07:53.187Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thats also why my model is on hold.</Text></Message><Message Date="24.01.2009" Time="19:08:08" DateTime="2009-01-24T18:08:08.375Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">i ant see to complete the skinning</Text></Message><Message Date="24.01.2009" Time="19:08:27" DateTime="2009-01-24T18:08:27.703Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">which part are you stuck on?</Text></Message><Message Date="24.01.2009" Time="19:09:19" DateTime="2009-01-24T18:09:19.046Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">arms and legs, also the hips</Text></Message><Message Date="24.01.2009" Time="19:10:24" DateTime="2009-01-24T18:10:24.515Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">can i try? wanna see if i get the same problem as i do with my own model</Text></Message><Message Date="24.01.2009" Time="19:11:08" DateTime="2009-01-24T18:11:08.562Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">you want my rig?</Text></Message><Message Date="24.01.2009" Time="19:11:20" DateTime="2009-01-24T18:11:20.000Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">without the bones</Text></Message><Message Date="24.01.2009" Time="19:11:41" DateTime="2009-01-24T18:11:41.703Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">or the rig</Text></Message><Message Date="24.01.2009" Time="19:11:47" DateTime="2009-01-24T18:11:47.718Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">my rig or my model?</Text></Message><Message Date="24.01.2009" Time="19:12:03" DateTime="2009-01-24T18:12:03.875Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">your model, just wanna try to skin it</Text></Message><Message Date="24.01.2009" Time="19:12:20" DateTime="2009-01-24T18:12:20.765Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok sure hold on</Text></Message><Message Date="24.01.2009" Time="19:21:38" DateTime="2009-01-24T18:21:38.484Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok here it is</Text></Message><Invitation Date="24.01.2009" Time="19:21:57" DateTime="2009-01-24T18:21:57.281Z" SessionID="13"><From><User FriendlyName="Fox"/></From><File>FujinAkuma.obj</File><Text Style="color:#545454; ">Fox sender FujinAkuma.obj</Text></Invitation><Message Date="24.01.2009" Time="19:22:07" DateTime="2009-01-24T18:22:07.625Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">it is an early verison of it</Text></Message><Message Date="24.01.2009" Time="19:22:24" DateTime="2009-01-24T18:22:24.875Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">not my final tweak</Text></Message><Message Date="24.01.2009" Time="19:22:34" DateTime="2009-01-24T18:22:34.453Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">mkay :)</Text></Message><Message Date="24.01.2009" Time="19:22:48" DateTime="2009-01-24T18:22:48.937Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but it should be fine for skinning</Text></Message><InvitationResponse Date="24.01.2009" Time="19:23:32" DateTime="2009-01-24T18:23:32.687Z" SessionID="13"><From><User FriendlyName="Fox"/></From><File>D:\Privat\Dokumenter\Mine mottatte filer\FujinAkuma.obj</File><Text Style="color:#545454; ">Du har mottatt D:\Privat\Dokumenter\Mine mottatte filer\FujinAkuma.obj fra Fox.</Text></InvitationResponse><Message Date="24.01.2009" Time="19:24:29" DateTime="2009-01-24T18:24:29.796Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">wow! nice work</Text></Message><Message Date="24.01.2009" Time="19:24:53" DateTime="2009-01-24T18:24:53.781Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">thanks</Text></Message><Message Date="24.01.2009" Time="19:25:11" DateTime="2009-01-24T18:25:11.765Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">but like i said that is not the final model</Text></Message><Message Date="24.01.2009" Time="19:47:31" DateTime="2009-01-24T18:47:31.984Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">let me know how it works out.</Text></Message><Message Date="24.01.2009" Time="19:47:43" DateTime="2009-01-24T18:47:43.953Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i will :) just setting up the bones</Text></Message><Message Date="24.01.2009" Time="19:47:55" DateTime="2009-01-24T18:47:55.734Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">cool</Text></Message><Message Date="24.01.2009" Time="20:33:54" DateTime="2009-01-24T19:33:54.562Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm getting frustrated </Text></Message><Message Date="24.01.2009" Time="20:34:16" DateTime="2009-01-24T19:34:16.734Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i can't believe i've done this before</Text></Message><Message Date="24.01.2009" Time="20:34:17" DateTime="2009-01-24T19:34:17.171Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">are you running into the same problems?</Text></Message><Message Date="24.01.2009" Time="20:34:20" DateTime="2009-01-24T19:34:20.046Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="24.01.2009" Time="20:34:43" DateTime="2009-01-24T19:34:43.609Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">yeah the same thing happeds to me</Text></Message><Message Date="24.01.2009" Time="20:35:15" DateTime="2009-01-24T19:35:15.984Z" SessionID="13"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm gonna go get some food</Text></Message><Message Date="24.01.2009" Time="20:35:56" DateTime="2009-01-24T19:35:56.390Z" SessionID="13"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">ok hehe</Text></Message><Message Date="02.02.2009" Time="18:27:06" DateTime="2009-02-02T17:27:06.272Z" SessionID="14"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey man</Text></Message><Message Date="02.02.2009" Time="18:27:26" DateTime="2009-02-02T17:27:26.334Z" SessionID="14"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i've seen you've pm'ed me, forgot to log off msn from work</Text></Message><Message Date="02.02.2009" Time="18:28:13" DateTime="2009-02-02T17:28:13.631Z" SessionID="14"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i didn't get much further on your model, been busy the last days</Text></Message><Message Date="02.02.2009" Time="18:28:24" DateTime="2009-02-02T17:28:24.318Z" SessionID="14"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have motionbuilder 2009, yes</Text></Message><Message Date="02.02.2009" Time="19:17:05" DateTime="2009-02-02T18:17:05.147Z" SessionID="15"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">oh thats cool</Text></Message><Message Date="02.02.2009" Time="19:18:28" DateTime="2009-02-02T18:18:28.662Z" SessionID="15"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i just played a bit with your model now, the legs/hips looks pretty decent (from my point of view)</Text></Message><Message Date="02.02.2009" Time="19:18:35" DateTime="2009-02-02T18:18:35.178Z" SessionID="15"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">i also had no luck with my skinning.</Text></Message><Message Date="02.02.2009" Time="19:20:09" DateTime="2009-02-02T18:20:09.225Z" SessionID="15"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">thanks trying to make my new model look better.</Text></Message><Message Date="10.02.2009" Time="21:19:31" DateTime="2009-02-10T20:19:31.140Z" SessionID="16"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">So what do you think about CAT?</Text></Message><Message Date="10.02.2009" Time="22:32:33" DateTime="2009-02-10T21:32:33.203Z" SessionID="16"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">havent had the time to look at it yet :)</Text></Message><Message Date="10.02.2009" Time="22:33:10" DateTime="2009-02-10T21:33:10.078Z" SessionID="17"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">oh ok</Text></Message><Message Date="15.02.2009" Time="22:39:57" DateTime="2009-02-15T21:39:57.343Z" SessionID="18"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:MS Shell Dlg; color:#000000; ">hay whats new??</Text></Message><Message Date="20.02.2009" Time="23:06:37" DateTime="2009-02-20T22:06:37.953Z" SessionID="19"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">hi i see you are very busy. i just wanted to know if you had time to check out CAT.</Text></Message><Message Date="17.03.2009" Time="23:43:05" DateTime="2009-03-17T22:43:05.765Z" SessionID="20"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">hi.</Text></Message><Message Date="28.10.2009" Time="19:22:05" DateTime="2009-10-28T18:22:05.234Z" SessionID="21"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey man, long time, how's it going?</Text></Message><Message Date="28.10.2009" Time="19:22:50" DateTime="2009-10-28T18:22:50.671Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">h</Text></Message><Message Date="28.10.2009" Time="19:22:53" DateTime="2009-10-28T18:22:53.796Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hi</Text></Message><Message Date="28.10.2009" Time="19:23:00" DateTime="2009-10-28T18:23:00.640Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">umm who am i chatting with??</Text></Message><Message Date="28.10.2009" Time="19:23:21" DateTime="2009-10-28T18:23:21.328Z" SessionID="22"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah, I met you through irc, think it was #3dsmax</Text></Message><Message Date="28.10.2009" Time="19:23:31" DateTime="2009-10-28T18:23:31.453Z" SessionID="22"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">:P</Text></Message><Message Date="28.10.2009" Time="19:23:41" DateTime="2009-10-28T18:23:41.531Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok i yeah i gang out on max channels alot</Text></Message><Message Date="28.10.2009" Time="19:24:02" DateTime="2009-10-28T18:24:02.359Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I guess it really has been awhile, because i cant remember who you are.</Text></Message><Message Date="28.10.2009" Time="19:24:36" DateTime="2009-10-28T18:24:36.140Z" SessionID="22"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe, yeah it's been some months ;) </Text></Message><Message Date="28.10.2009" Time="19:24:56" DateTime="2009-10-28T18:24:56.234Z" SessionID="22"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so, any cool 3d projects going on?</Text></Message><Message Date="28.10.2009" Time="19:25:26" DateTime="2009-10-28T18:25:26.265Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i'm working on a new model i'm trying to finish</Text></Message><Message Date="28.10.2009" Time="19:25:38" DateTime="2009-10-28T18:25:38.875Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">human character?</Text></Message><Message Date="28.10.2009" Time="19:26:55" DateTime="2009-10-28T18:26:55.843Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I began working on the shild a few days ago.http://home.earthlink.net/~gray_fox001/Sheld.jpg </Text></Message><Message Date="28.10.2009" Time="19:27:30" DateTime="2009-10-28T18:27:30.921Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">when i am finish with that i will start work on her sword which i really like.</Text></Message><Message Date="28.10.2009" Time="19:28:26" DateTime="2009-10-28T18:28:26.359Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="28.10.2009" Time="19:29:03" DateTime="2009-10-28T18:29:03.109Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">do you do the uv-mapping/unwrapping in 3ds max, or do you use a third party app btw?</Text></Message><Message Date="28.10.2009" Time="19:29:26" DateTime="2009-10-28T18:29:26.828Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i use a third party program</Text></Message><Message Date="28.10.2009" Time="19:29:43" DateTime="2009-10-28T18:29:43.515Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i cant get the hang of max uv unwrap for nothing.</Text></Message><Message Date="28.10.2009" Time="19:30:22" DateTime="2009-10-28T18:30:22.296Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">me neither, just tought i'd recommend you unfold3d if you were doing it within max </Text></Message><Message Date="28.10.2009" Time="19:32:40" DateTime="2009-10-28T18:32:40.609Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">are you still creating things in max??</Text></Message><Message Date="28.10.2009" Time="19:32:55" DateTime="2009-10-28T18:32:55.828Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i've switched over to maya since last time actually, i'm actually starting i like it better than max :)</Text></Message><Message Date="28.10.2009" Time="19:33:18" DateTime="2009-10-28T18:33:18.078Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe cool do you have anything to show.</Text></Message><Message Date="28.10.2009" Time="19:33:39" DateTime="2009-10-28T18:33:39.796Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://shcmack.blogg.no/images/537792-10-1254953346785.jpg</Text></Message><Message Date="28.10.2009" Time="19:33:48" DateTime="2009-10-28T18:33:48.000Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I dont think i will switch from max.. but i am trying to get the hang of xsi</Text></Message><Message Date="28.10.2009" Time="19:34:05" DateTime="2009-10-28T18:34:05.921Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">an alien character, i've done some re-modeling on the hands since that picture</Text></Message><Message Date="28.10.2009" Time="19:34:34" DateTime="2009-10-28T18:34:34.859Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool not mad at all</Text></Message><Message Date="28.10.2009" Time="19:34:34" DateTime="2009-10-28T18:34:34.906Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">why xsi? i've heard they got a pretty good particle system</Text></Message><Message Date="28.10.2009" Time="19:34:43" DateTime="2009-10-28T18:34:43.687Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yup</Text></Message><Message Date="28.10.2009" Time="19:34:44" DateTime="2009-10-28T18:34:44.828Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://shcmack.blogg.no/images/537792-10-1254953365235.jpg</Text></Message><Message Date="28.10.2009" Time="19:34:51" DateTime="2009-10-28T18:34:51.640Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i like how xsi work.</Text></Message><Message Date="28.10.2009" Time="19:35:51" DateTime="2009-10-28T18:35:51.125Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, havent tried it at all.. i tried out houdini, but i didn't find it logical at all :P everything's node-based</Text></Message><Message Date="28.10.2009" Time="19:35:53" DateTime="2009-10-28T18:35:53.375Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i see you need to add some more detail to the hands.</Text></Message><Message Date="28.10.2009" Time="19:36:20" DateTime="2009-10-28T18:36:20.500Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe</Text></Message><Message Date="28.10.2009" Time="19:37:00" DateTime="2009-10-28T18:37:00.046Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i did an animation too, just to get the hang of blendshapes in maya (same as morphing in max)</Text></Message><Message Date="28.10.2009" Time="19:37:06" DateTime="2009-10-28T18:37:06.546Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://vimeo.com/7132750</Text></Message><Message Date="28.10.2009" Time="19:38:08" DateTime="2009-10-28T18:38:08.390Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">wow thats cool dude i wish i could do animations like that.</Text></Message><Message Date="28.10.2009" Time="19:39:05" DateTime="2009-10-28T18:39:05.609Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it doesn't really take long time to learn, i did that in one week, after playing around with maya for a month</Text></Message><Message Date="28.10.2009" Time="19:39:06" DateTime="2009-10-28T18:39:06.156Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">a face i am working on http://home.earthlink.net/~gray_fox001/NewFaceFront.jpg</Text></Message><Message Date="28.10.2009" Time="19:39:23" DateTime="2009-10-28T18:39:23.203Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">wow.</Text></Message><Message Date="28.10.2009" Time="19:39:37" DateTime="2009-10-28T18:39:37.187Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice, got a wire?</Text></Message><Message Date="28.10.2009" Time="19:39:52" DateTime="2009-10-28T18:39:52.000Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no sorry i did not render out any.</Text></Message><Message Date="28.10.2009" Time="19:40:00" DateTime="2009-10-28T18:40:00.015Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">k</Text></Message><Message Date="28.10.2009" Time="19:40:18" DateTime="2009-10-28T18:40:18.890Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">another face i need to complete http://home.earthlink.net/~gray_fox001/MinaWithHair.jpg</Text></Message><Message Date="28.10.2009" Time="19:40:33" DateTime="2009-10-28T18:40:33.593Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">We should team up and do some models and animations.</Text></Message><Message Date="28.10.2009" Time="19:40:58" DateTime="2009-10-28T18:40:58.140Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">have you re-used this face on the other model? the lips looks exactly the same :P</Text></Message><Message Date="28.10.2009" Time="19:41:25" DateTime="2009-10-28T18:41:25.843Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no i always start over my models</Text></Message><Message Date="28.10.2009" Time="19:41:33" DateTime="2009-10-28T18:41:33.265Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, but it's a bit hard when using different apps :P</Text></Message><Message Date="28.10.2009" Time="19:41:33" DateTime="2009-10-28T18:41:33.609Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i never reuse parts.</Text></Message><Message Date="28.10.2009" Time="19:42:13" DateTime="2009-10-28T18:42:13.671Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i can always export my files i think maya can read .obj files</Text></Message><Message Date="28.10.2009" Time="19:42:16" DateTime="2009-10-28T18:42:16.640Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">good practice :) i tend to re-use parts of models sometime :/</Text></Message><Message Date="28.10.2009" Time="19:42:30" DateTime="2009-10-28T18:42:30.562Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thats why i like xsi it will take it max files and maya files</Text></Message><Message Date="28.10.2009" Time="19:43:09" DateTime="2009-10-28T18:43:09.265Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok now i remember who you are.. i just saw you pic icon.</Text></Message><Message Date="28.10.2009" Time="19:43:12" DateTime="2009-10-28T18:43:12.359Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yea, but it's harder when it comes to the rig and animation</Text></Message><Message Date="28.10.2009" Time="19:43:24" DateTime="2009-10-28T18:43:24.375Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">heheh :)</Text></Message><Message Date="28.10.2009" Time="19:45:01" DateTime="2009-10-28T18:45:01.609Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">really?</Text></Message><Message Date="28.10.2009" Time="19:45:05" DateTime="2009-10-28T18:45:05.031Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how so?</Text></Message><Message Date="28.10.2009" Time="19:45:54" DateTime="2009-10-28T18:45:54.593Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">first of, we have to use the same rig (or we should atleast), and you can't export a rig from maya to max</Text></Message><Message Date="28.10.2009" Time="19:46:30" DateTime="2009-10-28T18:46:30.968Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">can maya read fbx files?</Text></Message><Message Date="28.10.2009" Time="19:47:10" DateTime="2009-10-28T18:47:10.203Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yea, but fbx contains the model with keyframes</Text></Message><Message Date="28.10.2009" Time="19:47:23" DateTime="2009-10-28T18:47:23.328Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have a copy of maya 2010 i will install it and see if i can export amations from maya to max and from max to maya</Text></Message><Message Date="28.10.2009" Time="19:47:49" DateTime="2009-10-28T18:47:49.265Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you can export animation, but not the rig</Text></Message><Message Date="28.10.2009" Time="19:48:13" DateTime="2009-10-28T18:48:13.375Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I see... but there should be a way to do it.</Text></Message><Message Date="28.10.2009" Time="19:48:36" DateTime="2009-10-28T18:48:36.265Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">well maybe, but i don't know how</Text></Message><Message Date="28.10.2009" Time="19:50:07" DateTime="2009-10-28T18:50:07.062Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I will look into it, and see if i can.... If i find out how to do it will you be on board to create cool anmations for 3d models i create in max?</Text></Message><Message Date="28.10.2009" Time="19:52:36" DateTime="2009-10-28T18:52:36.968Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">well maybe, it's hard to say when i got the time and not, i got some projects i want to try to complete myself :) but just send me a message when you've got something finished</Text></Message><Message Date="28.10.2009" Time="19:52:49" DateTime="2009-10-28T18:52:49.937Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">character animation takes time</Text></Message><Message Date="28.10.2009" Time="19:53:10" DateTime="2009-10-28T18:53:10.296Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm just starting out, havent tried character animation at all yet</Text></Message><Message Date="28.10.2009" Time="19:53:15" DateTime="2009-10-28T18:53:15.046Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">or some, but not much</Text></Message><Message Date="28.10.2009" Time="19:53:26" DateTime="2009-10-28T18:53:26.031Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i know i did not mean for you to drop what you are doing to start working on animations for me.</Text></Message><Message Date="28.10.2009" Time="19:53:48" DateTime="2009-10-28T18:53:48.562Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I just meant it in if you want to do a project together we can.</Text></Message><Message Date="28.10.2009" Time="19:53:58" DateTime="2009-10-28T18:53:58.843Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah :)</Text></Message><Message Date="28.10.2009" Time="19:54:34" DateTime="2009-10-28T18:54:34.968Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i suck at animations so it wold be cool if  i had some one to help me with it.</Text></Message><Message Date="28.10.2009" Time="19:54:46" DateTime="2009-10-28T18:54:46.078Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and maye i can help you out ith modeling.</Text></Message><Message Date="28.10.2009" Time="19:54:55" DateTime="2009-10-28T18:54:55.796Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">:) are you using zbrush btw?</Text></Message><Message Date="28.10.2009" Time="19:55:21" DateTime="2009-10-28T18:55:21.609Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i have zbrush, but i'm not good with it. I'm still learning.</Text></Message><Message Date="28.10.2009" Time="19:55:48" DateTime="2009-10-28T18:55:48.328Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yea i'm gonna start looking into it myself</Text></Message><Message Date="28.10.2009" Time="19:56:11" DateTime="2009-10-28T18:56:11.156Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you shouldit rocks for adding alot of detail to your models.</Text></Message><Message Date="28.10.2009" Time="19:56:18" DateTime="2009-10-28T18:56:18.031Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="28.10.2009" Time="19:56:24" DateTime="2009-10-28T18:56:24.984Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i used it on my girl icon girl</Text></Message><Message Date="28.10.2009" Time="19:56:35" DateTime="2009-10-28T18:56:35.781Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and just add the details as normal-map or displacement</Text></Message><Message Date="28.10.2009" Time="19:56:45" DateTime="2009-10-28T18:56:45.796Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yup</Text></Message><Message Date="28.10.2009" Time="19:57:11" DateTime="2009-10-28T18:57:11.750Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i did it for clothes i model for my model and import it back to max it it look really good.</Text></Message><Message Date="28.10.2009" Time="19:57:31" DateTime="2009-10-28T18:57:31.062Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">got a picture?</Text></Message><Message Date="28.10.2009" Time="19:57:52" DateTime="2009-10-28T18:57:52.140Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no sorry i did not save the render</Text></Message><Message Date="28.10.2009" Time="19:58:16" DateTime="2009-10-28T18:58:16.000Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah, well i'm gonna try that myself</Text></Message><Message Date="28.10.2009" Time="19:58:32" DateTime="2009-10-28T18:58:32.343Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://home.earthlink.net/~gray_fox001/FujinAkumaClothesRender.jpg</Text></Message><Message Date="28.10.2009" Time="19:58:34" DateTime="2009-10-28T18:58:34.750Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok</Text></Message><Message Date="28.10.2009" Time="19:58:53" DateTime="2009-10-28T18:58:53.625Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i still need to fix the texturs on it</Text></Message><Message Date="28.10.2009" Time="19:59:09" DateTime="2009-10-28T18:59:09.375Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">did you make the quads in zbrush?</Text></Message><Message Date="28.10.2009" Time="19:59:20" DateTime="2009-10-28T18:59:20.750Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">or the fabric-style</Text></Message><Message Date="28.10.2009" Time="19:59:44" DateTime="2009-10-28T18:59:44.343Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">fabric-style</Text></Message><Message Date="28.10.2009" Time="20:00:01" DateTime="2009-10-28T19:00:01.390Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but it is not hard to do in abrush just create an alpher of it</Text></Message><Message Date="28.10.2009" Time="20:00:11" DateTime="2009-10-28T19:00:11.671Z" SessionID="22"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and paint it on the mesh/</Text></Message><Message Date="28.10.2009" Time="20:00:16" DateTime="2009-10-28T19:00:16.546Z" SessionID="22"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="29.10.2009" Time="20:12:52" DateTime="2009-10-29T19:12:52.684Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sorry, didnt see your message until now</Text></Message><Message Date="29.10.2009" Time="20:13:03" DateTime="2009-10-29T19:13:03.950Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah sure</Text></Message><Message Date="29.10.2009" Time="20:13:16" DateTime="2009-10-29T19:13:16.543Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh ok</Text></Message><Message Date="29.10.2009" Time="20:13:43" DateTime="2009-10-29T19:13:43.043Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i was just showing you what i add to the model.</Text></Message><Message Date="29.10.2009" Time="20:13:59" DateTime="2009-10-29T19:13:59.512Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">link?</Text></Message><Message Date="29.10.2009" Time="20:14:58" DateTime="2009-10-29T19:14:58.872Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://home.earthlink.net/~gray_fox001/Shield01.jpg</Text></Message><Message Date="29.10.2009" Time="20:16:37" DateTime="2009-10-29T19:16:37.559Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">heh, what have you added? :P</Text></Message><Message Date="29.10.2009" Time="20:17:40" DateTime="2009-10-29T19:17:40.325Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://home.earthlink.net/~gray_fox001/Sheld.jpg</Text></Message><Message Date="29.10.2009" Time="20:17:57" DateTime="2009-10-29T19:17:57.356Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that was how it look before.</Text></Message><Message Date="29.10.2009" Time="20:18:12" DateTime="2009-10-29T19:18:12.356Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah, hehe</Text></Message><Message Date="29.10.2009" Time="20:18:41" DateTime="2009-10-29T19:18:41.450Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://home.earthlink.net/~gray_fox001/b08.jpg</Text></Message><Message Date="29.10.2009" Time="20:18:48" DateTime="2009-10-29T19:18:48.528Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the blades are next.</Text></Message><Message Date="29.10.2009" Time="20:19:08" DateTime="2009-10-29T19:19:08.278Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice</Text></Message><Message Date="29.10.2009" Time="20:19:20" DateTime="2009-10-29T19:19:20.356Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you just need to rotate it</Text></Message><Message Date="29.10.2009" Time="20:19:49" DateTime="2009-10-29T19:19:49.481Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the lines</Text></Message><Message Date="29.10.2009" Time="20:20:09" DateTime="2009-10-29T19:20:09.715Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="29.10.2009" Time="20:20:53" DateTime="2009-10-29T19:20:53.715Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i like how it looks, but i could be wrong.</Text></Message><Message Date="29.10.2009" Time="20:22:15" DateTime="2009-10-29T19:22:15.825Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i actually think it'd look better if you rotated the lines, but it's your model ;)</Text></Message><Message Date="29.10.2009" Time="20:23:23" DateTime="2009-10-29T19:23:23.168Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i can rotate it.</Text></Message><Message Date="29.10.2009" Time="20:23:57" DateTime="2009-10-29T19:23:57.731Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i plan to animate it so i dont think it will matter where they are.</Text></Message><Message Date="29.10.2009" Time="20:24:34" DateTime="2009-10-29T19:24:34.090Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it shouln't matter :)</Text></Message><Message Date="29.10.2009" Time="20:25:00" DateTime="2009-10-29T19:25:00.293Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok</Text></Message><Message Date="29.10.2009" Time="20:28:02" DateTime="2009-10-29T19:28:02.418Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how is your work coming along?</Text></Message><Message Date="29.10.2009" Time="20:29:44" DateTime="2009-10-29T19:29:44.262Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">didn't i tell you? i've quit, gone back to school - graphic design, that's why i've started using maya :)</Text></Message><Message Date="29.10.2009" Time="20:31:55" DateTime="2009-10-29T19:31:55.075Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok well if you are doing graphic design in school you would be working on something.</Text></Message><Message Date="29.10.2009" Time="20:32:38" DateTime="2009-10-29T19:32:38.356Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe, true, i'm gonna start on a realism task on monday</Text></Message><Message Date="29.10.2009" Time="20:32:50" DateTime="2009-10-29T19:32:50.059Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not sure what to make</Text></Message><Message Date="29.10.2009" Time="20:32:53" DateTime="2009-10-29T19:32:53.668Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sweet.</Text></Message><Message Date="29.10.2009" Time="20:33:11" DateTime="2009-10-29T19:33:11.965Z" SessionID="23"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i was thinking of making a pool table or something, but i'm not sure</Text></Message><Message Date="29.10.2009" Time="20:34:24" DateTime="2009-10-29T19:34:24.668Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i see..</Text></Message><Message Date="29.10.2009" Time="20:34:46" DateTime="2009-10-29T19:34:46.543Z" SessionID="23"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; "> pool table would be fine.</Text></Message><Message Date="29.11.2009" Time="22:39:42" DateTime="2009-11-29T21:39:42.734Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hay whats new?</Text></Message><Message Date="29.11.2009" Time="22:40:13" DateTime="2009-11-29T21:40:13.671Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey man</Text></Message><Message Date="29.11.2009" Time="22:40:25" DateTime="2009-11-29T21:40:25.468Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not much, testd out some lipsyncing</Text></Message><Message Date="29.11.2009" Time="22:40:44" DateTime="2009-11-29T21:40:44.343Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool anything i can check out?</Text></Message><Message Date="29.11.2009" Time="22:41:31" DateTime="2009-11-29T21:41:31.281Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure
http://vimeo.com/7516824</Text></Message><Message Date="29.11.2009" Time="22:42:36" DateTime="2009-11-29T21:42:36.921Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what about you?</Text></Message><Message Date="29.11.2009" Time="22:42:38" DateTime="2009-11-29T21:42:38.968Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">wow that hot dude</Text></Message><Message Date="29.11.2009" Time="22:43:00" DateTime="2009-11-29T21:43:00.718Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">still working on my model, but i cant seem to create some parts that i want to do.</Text></Message><Message Date="29.11.2009" Time="22:43:14" DateTime="2009-11-29T21:43:14.078Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what kind of parts?</Text></Message><Message Date="29.11.2009" Time="22:44:05" DateTime="2009-11-29T21:44:05.984Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://home.earthlink.net/~gray_fox001/b07.jpg</Text></Message><Message Date="29.11.2009" Time="22:44:38" DateTime="2009-11-29T21:44:38.968Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i am trying to model the part that is clost to the blade.</Text></Message><Message Date="29.11.2009" Time="22:44:57" DateTime="2009-11-29T21:44:57.687Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the golden thing?</Text></Message><Message Date="29.11.2009" Time="22:45:05" DateTime="2009-11-29T21:45:05.500Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="29.11.2009" Time="22:45:57" DateTime="2009-11-29T21:45:57.156Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah, the thing in the middle makes it a bit of a hassle</Text></Message><Message Date="29.11.2009" Time="22:46:03" DateTime="2009-11-29T21:46:03.781Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">also that red parts that extend out of it</Text></Message><Message Date="29.11.2009" Time="22:46:10" DateTime="2009-11-29T21:46:10.781Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="29.11.2009" Time="22:46:13" DateTime="2009-11-29T21:46:13.140Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yea</Text></Message><Message Date="29.11.2009" Time="22:46:52" DateTime="2009-11-29T21:46:52.312Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i think i would have started in the middle, and then work my way through from there</Text></Message><Message Date="29.11.2009" Time="22:47:08" DateTime="2009-11-29T21:47:08.906Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i cant get past that http://home.earthlink.net/~gray_fox001/Sword.jpg</Text></Message><Message Date="29.11.2009" Time="22:47:15" DateTime="2009-11-29T21:47:15.343Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh</Text></Message><Message Date="29.11.2009" Time="22:48:11" DateTime="2009-11-29T21:48:11.750Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it's usually smart to start with the most complicated things, because then you don't need to think about the geometry</Text></Message><Message Date="29.11.2009" Time="22:48:14" DateTime="2009-11-29T21:48:14.984Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">too much</Text></Message><Message Date="29.11.2009" Time="22:49:25" DateTime="2009-11-29T21:49:25.109Z" SessionID="24"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i see.. that thing is i tried to start there, but i could not model it.</Text></Message><Message Date="29.11.2009" Time="22:49:57" DateTime="2009-11-29T21:49:57.796Z" SessionID="24"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i can give it a shot when i'm done playing this poker tournament :P</Text></Message><Message Date="01.12.2009" Time="17:29:18" DateTime="2009-12-01T16:29:18.046Z" SessionID="25"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://home.earthlink.net/~gray_fox001/Sword01.jpg</Text></Message><Message Date="02.12.2009" Time="19:45:16" DateTime="2009-12-02T18:45:16.468Z" SessionID="26"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">so what do you think about the dessign i model for the sword?</Text></Message><Message Date="02.12.2009" Time="19:47:06" DateTime="2009-12-02T18:47:06.312Z" SessionID="26"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, looks ok, but it's unsmoothed, right?</Text></Message><Message Date="02.12.2009" Time="19:49:35" DateTime="2009-12-02T18:49:35.234Z" SessionID="27"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">no that was smooth.</Text></Message><Message Date="02.12.2009" Time="19:49:45" DateTime="2009-12-02T18:49:45.046Z" SessionID="27"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">really?</Text></Message><Message Date="02.12.2009" Time="19:49:49" DateTime="2009-12-02T18:49:49.093Z" SessionID="27"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">can i see a wire?</Text></Message><Message Date="02.12.2009" Time="19:50:01" DateTime="2009-12-02T18:50:01.328Z" SessionID="27"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">i did not have any lights setup so maybe the shadows have it looking like it is not smooth.</Text></Message><Message Date="02.12.2009" Time="19:50:37" DateTime="2009-12-02T18:50:37.203Z" SessionID="27"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">if that's smoothed it's really good.. maybe a tad too hard edges</Text></Message><Message Date="02.12.2009" Time="19:50:56" DateTime="2009-12-02T18:50:56.078Z" SessionID="27"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but i'd like to see a wire, i'm curious how you solved it</Text></Message><Message Date="02.12.2009" Time="19:52:09" DateTime="2009-12-02T18:52:09.421Z" SessionID="27"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">thats what i think, but i could be wrong, and it just does not look right.</Text></Message><Message Date="02.12.2009" Time="19:53:04" DateTime="2009-12-02T18:53:04.921Z" SessionID="27"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">sure, but i will have to render the wire later, because i am on work.</Text></Message><Message Date="02.12.2009" Time="19:53:37" DateTime="2009-12-02T18:53:37.250Z" SessionID="27"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure thing</Text></Message><Message Date="04.12.2009" Time="19:10:08" DateTime="2009-12-04T18:10:08.171Z" SessionID="28"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey</Text></Message><Message Date="04.12.2009" Time="19:10:12" DateTime="2009-12-04T18:10:12.500Z" SessionID="28"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sap?</Text></Message><Message Date="04.12.2009" Time="19:10:29" DateTime="2009-12-04T18:10:29.343Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not mcuh and you??</Text></Message><Message Date="04.12.2009" Time="19:10:34" DateTime="2009-12-04T18:10:34.531Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://home.earthlink.net/~gray_fox001/Sword02.jpg</Text></Message><Message Date="04.12.2009" Time="19:10:47" DateTime="2009-12-04T18:10:47.734Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i am working on doing the wirefram renders now.</Text></Message><Message Date="04.12.2009" Time="19:10:49" DateTime="2009-12-04T18:10:49.062Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm modeling a character that i'm gonna animate</Text></Message><Message Date="04.12.2009" Time="19:10:56" DateTime="2009-12-04T18:10:56.156Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sweet</Text></Message><Message Date="04.12.2009" Time="19:10:58" DateTime="2009-12-04T18:10:58.906Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice</Text></Message><Message Date="04.12.2009" Time="19:11:02" DateTime="2009-12-04T18:11:02.062Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">can i see it?</Text></Message><Message Date="04.12.2009" Time="19:11:08" DateTime="2009-12-04T18:11:08.125Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure, 2 sec</Text></Message><Invitation Date="04.12.2009" Time="19:16:27" DateTime="2009-12-04T18:16:27.296Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\1.jpg</File><Text Style="color:#545454; ">Jørn sender C:\Documents and Settings\Administrator\Skrivebord\1.jpg</Text></Invitation><InvitationResponse Date="04.12.2009" Time="19:16:54" DateTime="2009-12-04T18:16:54.375Z" SessionID="29"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\1.jpg</File><Text Style="color:#545454; ">Overføring av "1.jpg" er fullført.</Text></InvitationResponse><Invitation Date="04.12.2009" Time="19:17:02" DateTime="2009-12-04T18:17:02.359Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\2.jpg</File><Text Style="color:#545454; ">Jørn sender C:\Documents and Settings\Administrator\Skrivebord\2.jpg</Text></Invitation><InvitationResponse Date="04.12.2009" Time="19:17:16" DateTime="2009-12-04T18:17:16.906Z" SessionID="29"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\2.jpg</File><Text Style="color:#545454; ">Overføring av "2.jpg" er fullført.</Text></InvitationResponse><Message Date="04.12.2009" Time="19:17:57" DateTime="2009-12-04T18:17:57.765Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool i see your using morph targests for the eyes</Text></Message><Message Date="04.12.2009" Time="19:18:04" DateTime="2009-12-04T18:18:04.390Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah :)</Text></Message><Invitation Date="04.12.2009" Time="19:18:10" DateTime="2009-12-04T18:18:10.187Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\3.jpg</File><Text Style="color:#545454; ">Jørn sender C:\Documents and Settings\Administrator\Skrivebord\3.jpg</Text></Invitation><InvitationResponse Date="04.12.2009" Time="19:18:16" DateTime="2009-12-04T18:18:16.984Z" SessionID="29"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\3.jpg</File><Text Style="color:#545454; ">Overføring av "3.jpg" er fullført.</Text></InvitationResponse><Message Date="04.12.2009" Time="19:18:29" DateTime="2009-12-04T18:18:29.609Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">using a small bump on the head-thingy</Text></Message><Message Date="04.12.2009" Time="19:18:47" DateTime="2009-12-04T18:18:47.953Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm not sure how to do the eyelashes though</Text></Message><Message Date="04.12.2009" Time="19:19:08" DateTime="2009-12-04T18:19:08.109Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh thats easy i know of a really good tut you can use to create them.</Text></Message><Message Date="04.12.2009" Time="19:19:31" DateTime="2009-12-04T18:19:31.234Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the problem isn't really to create them, the problem is that i want them to follow the eyelids :)</Text></Message><Message Date="04.12.2009" Time="19:19:52" DateTime="2009-12-04T18:19:52.546Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ahh i see.</Text></Message><Message Date="04.12.2009" Time="19:21:04" DateTime="2009-12-04T18:21:04.078Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you may need to make that part of the eye lids, or use a helper to animate them with th eeye lids.</Text></Message><Message Date="04.12.2009" Time="19:22:24" DateTime="2009-12-04T18:22:24.828Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">well i don't think i can make'm part of the eyelids, i would have to make 50 splits in the mesh to get enough geometry to create them</Text></Message><Message Date="04.12.2009" Time="19:22:47" DateTime="2009-12-04T18:22:47.718Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and i'm not sure how to constrain them to the eyelids if i make them separately</Text></Message><Message Date="04.12.2009" Time="19:23:45" DateTime="2009-12-04T18:23:45.562Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you can use bones with helpers to do it.</Text></Message><Message Date="04.12.2009" Time="19:24:05" DateTime="2009-12-04T18:24:05.656Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have a tut on how it is done i just need to find it.</Text></Message><Message Date="04.12.2009" Time="19:24:14" DateTime="2009-12-04T18:24:14.250Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">eh, what's a helper? :P i'm using maya</Text></Message><Message Date="04.12.2009" Time="19:24:40" DateTime="2009-12-04T18:24:40.296Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh well may should have helper ojects also.</Text></Message><Message Date="04.12.2009" Time="19:24:52" DateTime="2009-12-04T18:24:52.546Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i cant tell you where to find them, because i dont use maya.</Text></Message><Message Date="04.12.2009" Time="19:25:04" DateTime="2009-12-04T18:25:04.968Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">most 3d apps have helper objects.</Text></Message><Message Date="04.12.2009" Time="19:25:59" DateTime="2009-12-04T18:25:59.656Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yea, i guess they just use another word for it in maya</Text></Message><Message Date="04.12.2009" Time="19:26:23" DateTime="2009-12-04T18:26:23.796Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no i think it is that same word just look it up</Text></Message><Message Date="04.12.2009" Time="19:27:40" DateTime="2009-12-04T18:27:40.234Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">can't find anything regarding helpers in maya</Text></Message><Message Date="04.12.2009" Time="19:31:43" DateTime="2009-12-04T18:31:43.593Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">let me check</Text></Message><Message Date="04.12.2009" Time="19:42:28" DateTime="2009-12-04T18:42:28.140Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i am chatting with one of my frieds who use maya i will have an answer for you.</Text></Message><Message Date="04.12.2009" Time="19:42:44" DateTime="2009-12-04T18:42:44.140Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh nice :)</Text></Message><Message Date="04.12.2009" Time="19:48:12" DateTime="2009-12-04T18:48:12.218Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok this is what he said</Text></Message><Message Date="04.12.2009" Time="19:48:15" DateTime="2009-12-04T18:48:15.312Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">He could get the rivet script and then build rivets along the eye lids and parent the geo to that, is one method.
Fox says:
 cool
Sean says:
 Otherway, is he could make a nurbs or subd proxy for the eyelids, rig those, then duplicate off a spline from one of the edges of that proxy eyelid.  It'll be conected by history so it'll folow the geo.
Fox says:
 umm i dont know how good he is with maya, bu</Text></Message><Message Date="04.12.2009" Time="19:49:02" DateTime="2009-12-04T18:49:02.718Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">then he could animate to path, and make an eyelash, attach it to animate to the path, and then scrub forward duplicating.  if you can, make a script to vary the size and roatation.
 then I think you use a wire deformer on them to get them to move with the spline.</Text></Message><Message Date="04.12.2009" Time="19:49:59" DateTime="2009-12-04T18:49:59.468Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">even better he found you a tut on it http://www.creativecrash.com/maya/tutorials/modeling/c/creating-an-eyelash</Text></Message><Message Date="04.12.2009" Time="19:51:03" DateTime="2009-12-04T18:51:03.015Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">he told me he is searching for the second half of the tut now</Text></Message><Message Date="04.12.2009" Time="19:51:39" DateTime="2009-12-04T18:51:39.656Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that could work actually :) i know how to create the lashes with a script though, but the way of animating them was usefull :)</Text></Message><Message Date="04.12.2009" Time="19:51:50" DateTime="2009-12-04T18:51:50.781Z" SessionID="29"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">say thanks from me ;)</Text></Message><Message Date="04.12.2009" Time="19:52:23" DateTime="2009-12-04T18:52:23.125Z" SessionID="29"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; "> will</Text></Message><Message Date="04.12.2009" Time="22:48:30" DateTime="2009-12-04T21:48:30.359Z" SessionID="30"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://home.earthlink.net/~gray_fox001/Sword04.jpg</Text></Message><Message Date="06.12.2009" Time="01:47:44" DateTime="2009-12-06T00:47:44.895Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok i have finsh modeling the sword, but i am having alot of problems with the chainsaw part of it.</Text></Message><Message Date="06.12.2009" Time="01:57:28" DateTime="2009-12-06T00:57:28.129Z" SessionID="31"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey</Text></Message><Message Date="06.12.2009" Time="01:57:32" DateTime="2009-12-06T00:57:32.207Z" SessionID="31"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">lemme have a look</Text></Message><Message Date="06.12.2009" Time="01:59:33" DateTime="2009-12-06T00:59:33.223Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://home.earthlink.net/~gray_fox001/Sword05.jpg</Text></Message><Message Date="06.12.2009" Time="02:00:48" DateTime="2009-12-06T01:00:48.941Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i alteady have the blades model it is just get thing to all follow the path of the sword , and keep facing foward.</Text></Message><Message Date="06.12.2009" Time="02:00:57" DateTime="2009-12-06T01:00:57.988Z" SessionID="31"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah</Text></Message><Message Date="06.12.2009" Time="02:01:23" DateTime="2009-12-06T01:01:23.223Z" SessionID="31"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i know how to do it in maya, but not sure in max</Text></Message><Message Date="06.12.2009" Time="02:01:46" DateTime="2009-12-06T01:01:46.301Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how would you do it in maya??</Text></Message><Message Date="06.12.2009" Time="02:02:51" DateTime="2009-12-06T01:02:51.957Z" SessionID="31"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'd create a curve, use motion path to attach the shards, and use animation snapshot for each frame</Text></Message><Message Date="06.12.2009" Time="02:03:22" DateTime="2009-12-06T01:03:22.816Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i think i have a tut on it in max i just have to go through 3 300gd hard drives to find the tut on it</Text></Message><Message Date="06.12.2009" Time="02:03:25" DateTime="2009-12-06T01:03:25.754Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ahhh i see</Text></Message><Message Date="06.12.2009" Time="02:03:29" DateTime="2009-12-06T01:03:29.020Z" SessionID="31"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">lol</Text></Message><Message Date="06.12.2009" Time="02:04:21" DateTime="2009-12-06T01:04:21.707Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">in max i can use a path constrain, and a look at constrain to fix it, but i dont remember how to use them.</Text></Message><Message Date="06.12.2009" Time="02:04:32" DateTime="2009-12-06T01:04:32.066Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I did it once in a tut, but forgot how to do it.</Text></Message><Message Date="06.12.2009" Time="02:06:18" DateTime="2009-12-06T01:06:18.363Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i should have it fix by morning.</Text></Message><Message Date="06.12.2009" Time="02:07:54" DateTime="2009-12-06T01:07:54.973Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how is your eye animation coming along??</Text></Message><Message Date="06.12.2009" Time="02:08:04" DateTime="2009-12-06T01:08:04.379Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Where you able to get anything done with it?</Text></Message><Message Date="06.12.2009" Time="02:08:39" DateTime="2009-12-06T01:08:39.801Z" SessionID="31"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">actually i skipped it, it looked a bit girl'ish with the eyelashes :P</Text></Message><Message Date="06.12.2009" Time="02:08:47" DateTime="2009-12-06T01:08:47.895Z" SessionID="31"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm rigging my character now</Text></Message><Message Date="06.12.2009" Time="02:09:06" DateTime="2009-12-06T01:09:06.363Z" SessionID="31"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe i see</Text></Message><Message Date="06.12.2009" Time="19:06:12" DateTime="2009-12-06T18:06:12.390Z" SessionID="32"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hi i complete the sword</Text></Message><Message Date="06.12.2009" Time="19:06:18" DateTime="2009-12-06T18:06:18.703Z" SessionID="32"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yey, lemme see</Text></Message><Message Date="06.12.2009" Time="19:06:25" DateTime="2009-12-06T18:06:25.921Z" SessionID="32"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i also animate the blades.</Text></Message><Message Date="06.12.2009" Time="19:06:31" DateTime="2009-12-06T18:06:31.937Z" SessionID="32"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://www.vimeo.com/8013857</Text></Message><Message Date="06.12.2009" Time="19:07:09" DateTime="2009-12-06T18:07:09.078Z" SessionID="32"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice :) kickass sword</Text></Message><Message Date="06.12.2009" Time="19:08:40" DateTime="2009-12-06T18:08:40.031Z" SessionID="33"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks</Text></Message><Message Date="06.12.2009" Time="19:12:54" DateTime="2009-12-06T18:12:54.093Z" SessionID="34"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how is your animation coming along?</Text></Message><Message Date="06.12.2009" Time="19:13:11" DateTime="2009-12-06T18:13:11.593Z" SessionID="34"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm having some problems, so i gotta figure out how i'm gonna rig it</Text></Message><Message Date="06.12.2009" Time="19:13:24" DateTime="2009-12-06T18:13:24.187Z" SessionID="34"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i need atleast two points to control the center of gravity</Text></Message><Message Date="06.12.2009" Time="19:13:41" DateTime="2009-12-06T18:13:41.109Z" SessionID="34"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">actually contemplating whether i should use maya or motionbuilder too</Text></Message><Message Date="06.12.2009" Time="19:14:07" DateTime="2009-12-06T18:14:07.515Z" SessionID="34"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so i'm taking a short break from the rigging</Text></Message><Message Date="06.12.2009" Time="19:15:37" DateTime="2009-12-06T18:15:37.421Z" SessionID="34"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i understand.</Text></Message><Message Date="06.12.2009" Time="19:15:56" DateTime="2009-12-06T18:15:56.484Z" SessionID="34"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i am now moving on to model the girl.</Text></Message><Message Date="06.12.2009" Time="19:16:10" DateTime="2009-12-06T18:16:10.500Z" SessionID="34"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="11.12.2009" Time="22:24:35" DateTime="2009-12-11T21:24:35.187Z" SessionID="35"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">whas new?</Text></Message><Message Date="11.12.2009" Time="22:25:01" DateTime="2009-12-11T21:25:01.859Z" SessionID="35"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, not much.. i'm thinking about modeling a car</Text></Message><Message Date="11.12.2009" Time="22:25:07" DateTime="2009-12-11T21:25:07.937Z" SessionID="35"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">just not sure which :P</Text></Message><Message Date="11.12.2009" Time="22:25:10" DateTime="2009-12-11T21:25:10.656Z" SessionID="35"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and u?</Text></Message><Message Date="11.12.2009" Time="22:25:49" DateTime="2009-12-11T21:25:49.937Z" SessionID="35"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thining about how i am goig to model the girl for my project.</Text></Message><Message Date="11.12.2009" Time="22:26:05" DateTime="2009-12-11T21:26:05.140Z" SessionID="35"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that's a big project :P</Text></Message><Message Date="11.12.2009" Time="22:28:11" DateTime="2009-12-11T21:28:11.062Z" SessionID="35"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i know</Text></Message><Message Date="11.12.2009" Time="22:29:06" DateTime="2009-12-11T21:29:06.781Z" SessionID="35"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how is your project coming along?</Text></Message><Message Date="11.12.2009" Time="22:29:27" DateTime="2009-12-11T21:29:27.859Z" SessionID="35"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the animation project? with the ninja? well, i've taken a short break from it</Text></Message><Message Date="11.12.2009" Time="22:29:47" DateTime="2009-12-11T21:29:47.109Z" SessionID="35"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ahh i see</Text></Message><Message Date="11.12.2009" Time="22:30:28" DateTime="2009-12-11T21:30:28.015Z" SessionID="35"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thats one of the reasons why i have not started on the girl i am taking some time off to work it out first</Text></Message><Message Date="11.12.2009" Time="22:30:40" DateTime="2009-12-11T21:30:40.703Z" SessionID="35"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yea</Text></Message><Message Date="13.12.2009" Time="21:48:49" DateTime="2009-12-13T20:48:49.187Z" SessionID="36"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hi what do you use as your render in maya?</Text></Message><Message Date="13.12.2009" Time="21:48:56" DateTime="2009-12-13T20:48:56.109Z" SessionID="36"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">mental ray</Text></Message><Message Date="13.12.2009" Time="21:49:53" DateTime="2009-12-13T20:49:53.703Z" SessionID="36"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh ok. i'm trying to setup Ambient Occlusion, but i dont know how too.</Text></Message><Message Date="13.12.2009" Time="21:50:04" DateTime="2009-12-13T20:50:04.421Z" SessionID="36"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">in maya?</Text></Message><Message Date="13.12.2009" Time="21:50:47" DateTime="2009-12-13T20:50:47.593Z" SessionID="36"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">in max i am using final render.. maya also has final render so i was hoping you had it.</Text></Message><Message Date="13.12.2009" Time="21:51:06" DateTime="2009-12-13T20:51:06.875Z" SessionID="36"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah, well i know how to set it up in maya, but not in max</Text></Message><Message Date="13.12.2009" Time="21:51:20" DateTime="2009-12-13T20:51:20.703Z" SessionID="36"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but i don't do it in the render settings</Text></Message><Message Date="13.12.2009" Time="21:51:48" DateTime="2009-12-13T20:51:48.656Z" SessionID="36"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i assign a surface shader with ambient occlusion on the objects</Text></Message><Message Date="13.12.2009" Time="21:51:57" DateTime="2009-12-13T20:51:57.203Z" SessionID="36"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">or i use a render layer with occlusion pre-set</Text></Message><Message Date="13.12.2009" Time="21:52:58" DateTime="2009-12-13T20:52:58.875Z" SessionID="36"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ahh i see.</Text></Message><Message Date="13.12.2009" Time="21:53:22" DateTime="2009-12-13T20:53:22.812Z" SessionID="37"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i will try that thanks.</Text></Message><Message Date="13.12.2009" Time="21:53:29" DateTime="2009-12-13T20:53:29.656Z" SessionID="37"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">np</Text></Message><Message Date="20.12.2009" Time="03:00:53" DateTime="2009-12-20T02:00:53.859Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hi how is it going?</Text></Message><Message Date="20.12.2009" Time="03:22:10" DateTime="2009-12-20T02:22:10.765Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, it's all good :)</Text></Message><Message Date="20.12.2009" Time="03:22:13" DateTime="2009-12-20T02:22:13.703Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what about you?</Text></Message><Message Date="20.12.2009" Time="03:23:05" DateTime="2009-12-20T02:23:05.640Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not much just here watching movies.. I was trying to start my new female model, but i just could not get any work done.</Text></Message><Message Date="20.12.2009" Time="03:23:18" DateTime="2009-12-20T02:23:18.359Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe</Text></Message><Message Date="20.12.2009" Time="03:23:22" DateTime="2009-12-20T02:23:22.890Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">have you seen avatar yet?</Text></Message><Message Date="20.12.2009" Time="03:23:51" DateTime="2009-12-20T02:23:51.109Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i saw it in 3d it was really cool</Text></Message><Message Date="20.12.2009" Time="03:24:10" DateTime="2009-12-20T02:24:10.578Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">same here, sickest thing i've ever seen</Text></Message><Message Date="20.12.2009" Time="03:25:00" DateTime="2009-12-20T02:25:00.703Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i know i cant wait to get it on blu ray</Text></Message><Message Date="20.12.2009" Time="03:25:24" DateTime="2009-12-20T02:25:24.171Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'd like to see behind the scenes :)</Text></Message><Message Date="20.12.2009" Time="03:25:43" DateTime="2009-12-20T02:25:43.765Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thats why i want the blu ray</Text></Message><Message Date="20.12.2009" Time="03:26:10" DateTime="2009-12-20T02:26:10.656Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">:)</Text></Message><Message Date="20.12.2009" Time="03:27:20" DateTime="2009-12-20T02:27:20.984Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i really like how the brought that world to life</Text></Message><Message Date="20.12.2009" Time="03:27:42" DateTime="2009-12-20T02:27:42.515Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">But the story was not anything new.</Text></Message><Message Date="20.12.2009" Time="03:28:14" DateTime="2009-12-20T02:28:14.421Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how come? i liked the story, i liked everything actually</Text></Message><Message Date="20.12.2009" Time="03:28:28" DateTime="2009-12-20T02:28:28.328Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">haven't seen anything similar earlier</Text></Message><Message Date="20.12.2009" Time="03:29:26" DateTime="2009-12-20T02:29:26.718Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have</Text></Message><Message Date="20.12.2009" Time="03:29:35" DateTime="2009-12-20T02:29:35.640Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">which movie?</Text></Message><Message Date="20.12.2009" Time="03:31:49" DateTime="2009-12-20T02:31:49.671Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hold on i am trying to remember the name of that movie.</Text></Message><Message Date="20.12.2009" Time="03:38:26" DateTime="2009-12-20T02:38:26.703Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">damit what is that disney movie with the endians, and the guys who travel to the new world?</Text></Message><Message Date="20.12.2009" Time="03:40:04" DateTime="2009-12-20T02:40:04.921Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe, don't know :P pocahontas</Text></Message><Message Date="20.12.2009" Time="03:40:08" DateTime="2009-12-20T02:40:08.609Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">?:P</Text></Message><Message Date="20.12.2009" Time="03:40:10" DateTime="2009-12-20T02:40:10.328Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok now i found it it is called yeah</Text></Message><Message Date="20.12.2009" Time="03:40:26" DateTime="2009-12-20T02:40:26.468Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thats it same story but in space</Text></Message><Message Date="20.12.2009" Time="03:40:44" DateTime="2009-12-20T02:40:44.234Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what? pocahontas?</Text></Message><Message Date="20.12.2009" Time="03:40:49" DateTime="2009-12-20T02:40:49.250Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yes</Text></Message><Message Date="20.12.2009" Time="03:41:18" DateTime="2009-12-20T02:41:18.296Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">really?</Text></Message><Message Date="20.12.2009" Time="03:41:33" DateTime="2009-12-20T02:41:33.078Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yup</Text></Message><Message Date="20.12.2009" Time="03:41:44" DateTime="2009-12-20T02:41:44.906Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">same concept</Text></Message><Message Date="20.12.2009" Time="03:41:53" DateTime="2009-12-20T02:41:53.390Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">there's like ten years since i've seen it, so i don't know.. but can't recall anything similar to avatar :P</Text></Message><Message Date="20.12.2009" Time="03:42:55" DateTime="2009-12-20T02:42:55.578Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok lets see ppl living on an island that was not discover by some more advanced ppl who arrives and see only savages and want to take what is there.</Text></Message><Message Date="20.12.2009" Time="03:43:00" DateTime="2009-12-20T02:43:00.375Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">see same concept</Text></Message><Message Date="20.12.2009" Time="03:43:03" DateTime="2009-12-20T02:43:03.640Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nothing new.</Text></Message><Message Date="20.12.2009" Time="03:43:58" DateTime="2009-12-20T02:43:58.265Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh well, don't know if you can create a movie with a story you can't compare to anything created before</Text></Message><Message Date="20.12.2009" Time="03:44:37" DateTime="2009-12-20T02:44:37.765Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what are you talking about i just told you the concpt they are the same.</Text></Message><Message Date="20.12.2009" Time="03:44:49" DateTime="2009-12-20T02:44:49.968Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">just on another planet.</Text></Message><Message Date="20.12.2009" Time="03:47:11" DateTime="2009-12-20T02:47:11.406Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i didn't argue about that</Text></Message><Message Date="20.12.2009" Time="03:47:29" DateTime="2009-12-20T02:47:29.109Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok</Text></Message><Message Date="20.12.2009" Time="03:47:59" DateTime="2009-12-20T02:47:59.484Z" SessionID="38"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">like i said, there's like ten years since i saw that movie, so i can't recall</Text></Message><Message Date="20.12.2009" Time="03:49:02" DateTime="2009-12-20T02:49:02.921Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe ok you should check it out some time you would see it is the same concept.</Text></Message><Message Date="20.12.2009" Time="03:49:26" DateTime="2009-12-20T02:49:26.984Z" SessionID="38"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but avatar was a very good movie i really love the effects.</Text></Message><Message Date="28.12.2009" Time="17:16:53" DateTime="2009-12-28T16:16:53.046Z" SessionID="39"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">whats new?</Text></Message><Message Date="28.12.2009" Time="17:30:32" DateTime="2009-12-28T16:30:32.750Z" SessionID="39"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey</Text></Message><Message Date="28.12.2009" Time="17:30:35" DateTime="2009-12-28T16:30:35.687Z" SessionID="39"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not much really</Text></Message><Message Date="28.12.2009" Time="17:30:54" DateTime="2009-12-28T16:30:54.906Z" SessionID="39"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how about you?</Text></Message><Message Date="28.12.2009" Time="17:45:50" DateTime="2009-12-28T16:45:50.718Z" SessionID="40"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">i am fine. how was chrustmas?</Text></Message><Message Date="09.01.2010" Time="18:51:35" DateTime="2010-01-09T17:51:35.437Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, didn't see your message until now</Text></Message><Message Date="09.01.2010" Time="18:51:48" DateTime="2010-01-09T17:51:48.437Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thats ok</Text></Message><Message Date="09.01.2010" Time="18:51:49" DateTime="2010-01-09T17:51:49.750Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">tend to forget to logout when i'm not home :P</Text></Message><Message Date="09.01.2010" Time="18:51:51" DateTime="2010-01-09T17:51:51.140Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">whats new?</Text></Message><Message Date="09.01.2010" Time="18:51:57" DateTime="2010-01-09T17:51:57.703Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">well, i'</Text></Message><Message Date="09.01.2010" Time="18:52:09" DateTime="2010-01-09T17:52:09.453Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i've created a new model that i'm gonna animate :P</Text></Message><Message Date="09.01.2010" Time="18:52:20" DateTime="2010-01-09T17:52:20.593Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and i'm almost done rigging it</Text></Message><Message Date="09.01.2010" Time="18:52:22" DateTime="2010-01-09T17:52:22.421Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sweet anything i can see?</Text></Message><Message Date="09.01.2010" Time="18:52:36" DateTime="2010-01-09T17:52:36.156Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://shcmack.site90.com/Animasjon.jpg</Text></Message><Message Date="09.01.2010" Time="18:52:40" DateTime="2010-01-09T17:52:40.734Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I have been changing around on what i want to do.</Text></Message><Message Date="09.01.2010" Time="18:52:50" DateTime="2010-01-09T17:52:50.953Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yea?</Text></Message><Message Date="09.01.2010" Time="18:53:14" DateTime="2010-01-09T17:53:14.031Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i kinda put my female mode on hold to work on a space ship.</Text></Message><Message Date="09.01.2010" Time="18:53:31" DateTime="2010-01-09T17:53:31.515Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I found some really cool concepts i want to do.</Text></Message><Message Date="09.01.2010" Time="18:53:44" DateTime="2010-01-09T17:53:44.781Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">wow he looks cool!!!</Text></Message><Message Date="09.01.2010" Time="18:53:48" DateTime="2010-01-09T17:53:48.015Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool, can i see?</Text></Message><Message Date="09.01.2010" Time="18:54:08" DateTime="2010-01-09T17:54:08.468Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks :) tried to make the arms and the legs really simple</Text></Message><Message Date="09.01.2010" Time="18:54:37" DateTime="2010-01-09T17:54:37.703Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah because tey are hard to skin with the more polygons on them</Text></Message><Message Date="09.01.2010" Time="18:54:46" DateTime="2010-01-09T17:54:46.078Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">exactly :)</Text></Message><Message Date="09.01.2010" Time="18:54:52" DateTime="2010-01-09T17:54:52.937Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i really hate skinning te croch area.</Text></Message><Message Date="09.01.2010" Time="18:54:59" DateTime="2010-01-09T17:54:59.343Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">me too!</Text></Message><Message Date="09.01.2010" Time="18:55:10" DateTime="2010-01-09T17:55:10.062Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but the jaw is harder actually</Text></Message><Message Date="09.01.2010" Time="18:55:27" DateTime="2010-01-09T17:55:27.765Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that's the only thing i'm stuck with now</Text></Message><Message Date="09.01.2010" Time="18:55:46" DateTime="2010-01-09T17:55:46.515Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ahh i see.</Text></Message><Message Date="09.01.2010" Time="18:55:55" DateTime="2010-01-09T17:55:55.781Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how do you rig your jaw?/</Text></Message><Message Date="09.01.2010" Time="18:56:05" DateTime="2010-01-09T17:56:05.656Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool, looks like a classic spaceship :)</Text></Message><Message Date="09.01.2010" Time="18:56:13" DateTime="2010-01-09T17:56:13.625Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it is a simple ship, but i plane to do more to it.</Text></Message><Message Date="09.01.2010" Time="18:56:29" DateTime="2010-01-09T17:56:29.875Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i just create a bone, and then add the controll later on</Text></Message><Message Date="09.01.2010" Time="18:56:38" DateTime="2010-01-09T17:56:38.078Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it will be like a time line on where we started from to where we are going.</Text></Message><Message Date="09.01.2010" Time="18:56:40" DateTime="2010-01-09T17:56:40.046Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but i'm not doing anything advanced with it</Text></Message><Message Date="09.01.2010" Time="18:57:02" DateTime="2010-01-09T17:57:02.203Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have 2 other ships i plan to make and i am working on the details on the inside of it.</Text></Message><Message Date="09.01.2010" Time="18:57:23" DateTime="2010-01-09T17:57:23.390Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I also plan to do an animation with it flying through space.</Text></Message><Message Date="09.01.2010" Time="18:57:23" DateTime="2010-01-09T17:57:23.984Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah, cool :) you're doing the inside too :-O</Text></Message><Message Date="09.01.2010" Time="18:57:31" DateTime="2010-01-09T17:57:31.390Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yup</Text></Message><Message Date="09.01.2010" Time="18:57:38" DateTime="2010-01-09T17:57:38.890Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">trying to get as detail as i can.</Text></Message><Message Date="09.01.2010" Time="18:58:05" DateTime="2010-01-09T17:58:05.781Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I am using fumefx for the trusters.</Text></Message><Message Date="09.01.2010" Time="18:58:09" DateTime="2010-01-09T17:58:09.015Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that a lot of work</Text></Message><Message Date="09.01.2010" Time="18:58:18" DateTime="2010-01-09T17:58:18.281Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, for fire</Text></Message><Message Date="09.01.2010" Time="18:58:27" DateTime="2010-01-09T17:58:27.562Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yup.</Text></Message><Message Date="09.01.2010" Time="18:59:14" DateTime="2010-01-09T17:59:14.812Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I also model out the solar </Text></Message><Message Date="09.01.2010" Time="18:59:17" DateTime="2010-01-09T17:59:17.578Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">syste</Text></Message><Message Date="09.01.2010" Time="18:59:46" DateTime="2010-01-09T17:59:46.875Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sounds like a good idea to switch around on how you do things, not to get tired of the project :)</Text></Message><Message Date="09.01.2010" Time="18:59:53" DateTime="2010-01-09T17:59:53.578Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have it with real dimentions it is huge.</Text></Message><Message Date="09.01.2010" Time="19:00:09" DateTime="2010-01-09T18:00:09.687Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">my shipe looks like a really really really small dot from far away.</Text></Message><Message Date="09.01.2010" Time="19:00:24" DateTime="2010-01-09T18:00:24.359Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the earthing also looks really small from far out.</Text></Message><Message Date="09.01.2010" Time="19:01:13" DateTime="2010-01-09T18:01:13.843Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I am trying to work out a scene where you can see the ship take off and leave through the antmostpheare.</Text></Message><Message Date="09.01.2010" Time="19:01:30" DateTime="2010-01-09T18:01:30.281Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">That part will be hard.</Text></Message><Message Date="09.01.2010" Time="19:02:04" DateTime="2010-01-09T18:02:04.437Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">It would be cool if we can join our scene with your guy animated inside the ship controlling it</Text></Message><Message Date="09.01.2010" Time="19:02:09" DateTime="2010-01-09T18:02:09.187Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">That would be cool</Text></Message><Message Date="09.01.2010" Time="19:02:22" DateTime="2010-01-09T18:02:22.875Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">well for something if we both get better.</Text></Message><Message Date="09.01.2010" Time="19:02:47" DateTime="2010-01-09T18:02:47.937Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok i tihnk i said too much LOL!!</Text></Message><Message Date="09.01.2010" Time="19:04:21" DateTime="2010-01-09T18:04:21.578Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">grabbed some food :)</Text></Message><Message Date="09.01.2010" Time="19:04:35" DateTime="2010-01-09T18:04:35.031Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh cool.</Text></Message><Message Date="09.01.2010" Time="19:05:43" DateTime="2010-01-09T18:05:43.281Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that's gonna be hard, when passing through the athmosphere - when (atleast i don't) know how it should look like :P</Text></Message><Message Date="09.01.2010" Time="19:06:33" DateTime="2010-01-09T18:06:33.187Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe i found some cool tuts on how i can get something like it done.</Text></Message><Message Date="09.01.2010" Time="19:07:06" DateTime="2010-01-09T18:07:06.359Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">time will show, could be cool - got a lot of work to do before you come the the point where the only thing left is the driver of the spaceship :)</Text></Message><Message Date="09.01.2010" Time="19:07:33" DateTime="2010-01-09T18:07:33.578Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="09.01.2010" Time="19:07:55" DateTime="2010-01-09T18:07:55.703Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I may also just model some guy for it.</Text></Message><Message Date="09.01.2010" Time="19:08:05" DateTime="2010-01-09T18:08:05.640Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but the rigging part will be a pain.</Text></Message><Message Date="09.01.2010" Time="19:09:02" DateTime="2010-01-09T18:09:02.031Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">if i can find a way to get your rig or animation to work in max would you rig and skin the guy i model?</Text></Message><Message Date="09.01.2010" Time="19:09:42" DateTime="2010-01-09T18:09:42.781Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i don't think that's possible</Text></Message><Message Date="09.01.2010" Time="19:10:11" DateTime="2010-01-09T18:10:11.593Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but if there is a way would you do it.. hell i will even give you the model of the guy.</Text></Message><Message Date="09.01.2010" Time="19:10:52" DateTime="2010-01-09T18:10:52.000Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you know, there is those autorigging scripts, atleast there is for maya</Text></Message><Message Date="09.01.2010" Time="19:10:56" DateTime="2010-01-09T18:10:56.640Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i think it is for max to</Text></Message><Message Date="09.01.2010" Time="19:11:18" DateTime="2010-01-09T18:11:18.234Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have not seen any for max,</Text></Message><Message Date="09.01.2010" Time="19:11:58" DateTime="2010-01-09T18:11:58.250Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i talked to a guy with an awsome rig in max, he used these tutorials: https://www.cg-academy.net/es_catalog/index.php?cPath=22_40&amp;osCsid=4tvmrr2t763nmsm33s9e4nmv5b112lh7</Text></Message><Message Date="09.01.2010" Time="19:12:33" DateTime="2010-01-09T18:12:33.015Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i have thoes tuts already.</Text></Message><Message Date="09.01.2010" Time="19:12:47" DateTime="2010-01-09T18:12:47.718Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but skning for me is not easy.</Text></Message><Message Date="09.01.2010" Time="19:13:18" DateTime="2010-01-09T18:13:18.078Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have the rig already made i just cant seem to skin my model to look good when deformaed.</Text></Message><Message Date="09.01.2010" Time="19:13:33" DateTime="2010-01-09T18:13:33.843Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">also it is alot of time to put into it.</Text></Message><Message Date="09.01.2010" Time="19:14:00" DateTime="2010-01-09T18:14:00.406Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Trying to create a cool scene and learn rigging and skinning is alot to do at onec.</Text></Message><Message Date="09.01.2010" Time="19:14:45" DateTime="2010-01-09T18:14:45.234Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i see, skinning is a hard :/</Text></Message><Message Date="09.01.2010" Time="19:14:54" DateTime="2010-01-09T18:14:54.984Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, true</Text></Message><Message Date="09.01.2010" Time="19:15:18" DateTime="2010-01-09T18:15:18.671Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">But it is cool if you dont want to do it i understand.</Text></Message><Message Date="09.01.2010" Time="19:15:44" DateTime="2010-01-09T18:15:44.828Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have te facial rig tut it is really cool.</Text></Message><Message Date="09.01.2010" Time="19:15:53" DateTime="2010-01-09T18:15:53.031Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you should give it a try.</Text></Message><Message Date="09.01.2010" Time="19:16:29" DateTime="2010-01-09T18:16:29.218Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have it too, but i'm starting off a bit easy</Text></Message><Message Date="09.01.2010" Time="19:16:46" DateTime="2010-01-09T18:16:46.718Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">just to get the grasp of rigging, constraining and parenting</Text></Message><Message Date="09.01.2010" Time="19:17:08" DateTime="2010-01-09T18:17:08.625Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">if i don't want to do what? :P</Text></Message><Message Date="09.01.2010" Time="19:17:21" DateTime="2010-01-09T18:17:21.953Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">rig and skin the guy i will model.</Text></Message><Message Date="09.01.2010" Time="19:17:48" DateTime="2010-01-09T18:17:48.468Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah, well there's no point if it can't be exported to max, is there?</Text></Message><Message Date="09.01.2010" Time="19:18:07" DateTime="2010-01-09T18:18:07.453Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">like i said i will give him to you so you can do what ever you want with it.</Text></Message><Message Date="09.01.2010" Time="19:18:22" DateTime="2010-01-09T18:18:22.359Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">like i said if i fnd a way will you do it.</Text></Message><Message Date="09.01.2010" Time="19:18:32" DateTime="2010-01-09T18:18:32.734Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure</Text></Message><Message Date="09.01.2010" Time="19:18:38" DateTime="2010-01-09T18:18:38.765Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">:)</Text></Message><Message Date="09.01.2010" Time="19:18:53" DateTime="2010-01-09T18:18:53.546Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool i will do my research on it.</Text></Message><Message Date="09.01.2010" Time="19:27:48" DateTime="2010-01-09T18:27:48.156Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">do you have any concepts on how you would like to guy to looks?</Text></Message><Message Date="09.01.2010" Time="19:28:03" DateTime="2010-01-09T18:28:03.875Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not really</Text></Message><Message Date="09.01.2010" Time="19:28:10" DateTime="2010-01-09T18:28:10.187Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">k</Text></Message><Message Date="09.01.2010" Time="19:30:54" DateTime="2010-01-09T18:30:54.296Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">check this out http://www.creativecrash.com/maya/downloads/scripts-plugins/data-management/c/maya2max</Text></Message><Message Date="09.01.2010" Time="19:32:59" DateTime="2010-01-09T18:32:59.156Z" SessionID="41"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that's from 2000, it's easier to use fbx to export the animation - but that means that the animation has to be done within maya</Text></Message><Message Date="09.01.2010" Time="19:33:28" DateTime="2010-01-09T18:33:28.937Z" SessionID="41"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i was just chatting with some one who told me the same thing.</Text></Message><Message Date="10.01.2010" Time="22:24:42" DateTime="2010-01-10T21:24:42.156Z" SessionID="42"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hay</Text></Message><Message Date="10.01.2010" Time="22:24:49" DateTime="2010-01-10T21:24:49.375Z" SessionID="42"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have a question if you have 2 mins</Text></Message><Message Date="10.01.2010" Time="22:39:05" DateTime="2010-01-10T21:39:05.359Z" SessionID="42"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey</Text></Message><Message Date="10.01.2010" Time="22:39:08" DateTime="2010-01-10T21:39:08.281Z" SessionID="42"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="10.01.2010" Time="22:39:51" DateTime="2010-01-10T21:39:51.187Z" SessionID="42"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hi was it you who showed me a render you made with a  bullet or a ball going through a wall</Text></Message><Message Date="10.01.2010" Time="22:40:15" DateTime="2010-01-10T21:40:15.203Z" SessionID="42"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no, i had one going through a glass</Text></Message><Message Date="10.01.2010" Time="22:40:43" DateTime="2010-01-10T21:40:43.156Z" SessionID="42"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool can i see it again?</Text></Message><Message Date="10.01.2010" Time="22:41:01" DateTime="2010-01-10T21:41:01.562Z" SessionID="42"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://shcmack.blogg.no/images/537792-5-1260267848221.jpg</Text></Message><Message Date="10.01.2010" Time="22:41:06" DateTime="2010-01-10T21:41:06.046Z" SessionID="42"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Also how did you set it up?? if you used a tut can you link me to it??</Text></Message><Message Date="10.01.2010" Time="22:41:33" DateTime="2010-01-10T21:41:33.765Z" SessionID="42"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i didn't use any tutorials, i used a plugin called "blastcode"</Text></Message><Message Date="10.01.2010" Time="22:42:18" DateTime="2010-01-10T21:42:18.140Z" SessionID="42"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh ok so the it is not a dynamic sim?</Text></Message><Message Date="10.01.2010" Time="22:42:36" DateTime="2010-01-10T21:42:36.031Z" SessionID="42"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yes it is</Text></Message><Message Date="10.01.2010" Time="22:42:48" DateTime="2010-01-10T21:42:48.328Z" SessionID="42"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but it was done with blastcode</Text></Message><Message Date="10.01.2010" Time="22:43:04" DateTime="2010-01-10T21:43:04.671Z" SessionID="42"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok i will look into that pilgin</Text></Message><Message Date="10.01.2010" Time="22:48:58" DateTime="2010-01-10T21:48:58.531Z" SessionID="43"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">wow that is a cool plugin</Text></Message><Message Date="23.01.2010" Time="01:24:38" DateTime="2010-01-23T00:24:38.203Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">whats new??</Text></Message><Message Date="23.01.2010" Time="01:24:46" DateTime="2010-01-23T00:24:46.656Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey</Text></Message><Message Date="23.01.2010" Time="01:25:08" DateTime="2010-01-23T00:25:08.156Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">animating my character at the time, got about two shots ready</Text></Message><Message Date="23.01.2010" Time="01:25:20" DateTime="2010-01-23T00:25:20.625Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sweet</Text></Message><Message Date="23.01.2010" Time="01:25:33" DateTime="2010-01-23T00:25:33.375Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how about you_?</Text></Message><Message Date="23.01.2010" Time="01:25:58" DateTime="2010-01-23T00:25:58.562Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">working on my 3d models, and trying to create my outter space scene.</Text></Message><Message Date="23.01.2010" Time="01:28:17" DateTime="2010-01-23T00:28:17.343Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Anything i can look at??</Text></Message><Message Date="23.01.2010" Time="01:29:42" DateTime="2010-01-23T00:29:42.390Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not really, just done the animation sequences, havent really rendered anything yet, still have to animate the camera movements</Text></Message><Message Date="23.01.2010" Time="01:30:00" DateTime="2010-01-23T00:30:00.796Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">this is the look of the animation
http://shcmack.site90.com/ko3.jpg</Text></Message><Message Date="23.01.2010" Time="01:30:18" DateTime="2010-01-23T00:30:18.796Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">almost atleast</Text></Message><Message Date="23.01.2010" Time="01:31:01" DateTime="2010-01-23T00:31:01.390Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice</Text></Message><Message Date="23.01.2010" Time="01:31:13" DateTime="2010-01-23T00:31:13.375Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thats alot more than what i have.</Text></Message><Message Date="23.01.2010" Time="01:31:34" DateTime="2010-01-23T00:31:34.578Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">gonna change the kicker, make the walls lower, and add some graffiti on the walls</Text></Message><Message Date="23.01.2010" Time="01:31:44" DateTime="2010-01-23T00:31:44.265Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">anything you can show?</Text></Message><Message Date="23.01.2010" Time="01:33:14" DateTime="2010-01-23T00:33:14.781Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">let me check i did not save any of the renders i made, but i may have something.</Text></Message><Invitation Date="23.01.2010" Time="01:34:09" DateTime="2010-01-23T00:34:09.890Z" SessionID="44"><From><User FriendlyName="Fox"/></From><File>Island.jpg</File><Text Style="color:#545454; ">Fox sender Island.jpg</Text></Invitation><InvitationResponse Date="23.01.2010" Time="01:34:22" DateTime="2010-01-23T00:34:22.750Z" SessionID="44"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\Island.jpg</File><Text Style="color:#545454; ">Du har mottatt C:\Documents and Settings\Administrator\Skrivebord\Island.jpg fra Fox.</Text></InvitationResponse><Message Date="23.01.2010" Time="01:34:41" DateTime="2010-01-23T00:34:41.187Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what did you use to make this?</Text></Message><Message Date="23.01.2010" Time="01:35:33" DateTime="2010-01-23T00:35:33.437Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">A program called Vue 8</Text></Message><Message Date="23.01.2010" Time="01:35:53" DateTime="2010-01-23T00:35:53.031Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">really cool program that is intergrated with baoth max and maya</Text></Message><Message Date="23.01.2010" Time="01:35:59" DateTime="2010-01-23T00:35:59.937Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and some other progerams</Text></Message><Message Date="23.01.2010" Time="01:36:21" DateTime="2010-01-23T00:36:21.953Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, looked like it was made with vue :P</Text></Message><Message Date="23.01.2010" Time="01:37:29" DateTime="2010-01-23T00:37:29.031Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i'm trying to mess with it for my outdoor scene.</Text></Message><Message Date="23.01.2010" Time="01:38:08" DateTime="2010-01-23T00:38:08.843Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it's great for those purposes</Text></Message><Message Date="23.01.2010" Time="01:38:30" DateTime="2010-01-23T00:38:30.734Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i know.</Text></Message><Message Date="23.01.2010" Time="01:40:38" DateTime="2010-01-23T00:40:38.656Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I got startrd on my new female model for the sword and sheld i made.</Text></Message><Message Date="23.01.2010" Time="01:41:24" DateTime="2010-01-23T00:41:24.515Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">But i sont like how it is coming out... the neck looks bad. The ref image i am using is not lineing up the way i want them so it is hard to work on it.</Text></Message><Message Date="23.01.2010" Time="01:44:23" DateTime="2010-01-23T00:44:23.812Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it isn't easy :P</Text></Message><Invitation Date="23.01.2010" Time="01:44:44" DateTime="2010-01-23T00:44:44.937Z" SessionID="44"><From><User FriendlyName="Fox"/></From><File>Milim.jpg</File><Text Style="color:#545454; ">Fox sender Milim.jpg</Text></Invitation><Message Date="23.01.2010" Time="01:44:51" DateTime="2010-01-23T00:44:51.015Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">whats wthat i have so far</Text></Message><InvitationResponse Date="23.01.2010" Time="01:45:22" DateTime="2010-01-23T00:45:22.812Z" SessionID="44"><From><User FriendlyName="Fox"/></From><File>D:\\Mine mottatte filer\Milim.jpg</File><Text Style="color:#545454; ">Du har mottatt D:\\Mine mottatte filer\Milim.jpg fra Fox.</Text></InvitationResponse><Message Date="23.01.2010" Time="01:46:13" DateTime="2010-01-23T00:46:13.156Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">looks good, i usually block out first though, and then add edges and details later on</Text></Message><Message Date="23.01.2010" Time="01:46:25" DateTime="2010-01-23T00:46:25.750Z" SessionID="44"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but there's no right or wrong in modeling :)</Text></Message><Message Date="23.01.2010" Time="01:46:37" DateTime="2010-01-23T00:46:37.703Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i like to model and add my edges while i go.</Text></Message><Message Date="23.01.2010" Time="01:46:44" DateTime="2010-01-23T00:46:44.281Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">true</Text></Message><Message Date="23.01.2010" Time="01:47:25" DateTime="2010-01-23T00:47:25.328Z" SessionID="44"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I'm trying to have the upper half of the body complete tonight</Text></Message><Message Date="26.01.2010" Time="22:23:23" DateTime="2010-01-26T21:23:23.937Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">do you have an ipod touch?/</Text></Message><Message Date="26.01.2010" Time="22:23:35" DateTime="2010-01-26T21:23:35.812Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nope, why?</Text></Message><Message Date="26.01.2010" Time="22:24:02" DateTime="2010-01-26T21:24:02.531Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh i'm trying ot onvert some videos to play on it but i'm not getting them to work.</Text></Message><Message Date="26.01.2010" Time="22:24:14" DateTime="2010-01-26T21:24:14.781Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i see :P</Text></Message><Message Date="26.01.2010" Time="22:24:34" DateTime="2010-01-26T21:24:34.796Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah not working out at all i'm so pissed.</Text></Message><Message Date="26.01.2010" Time="22:25:06" DateTime="2010-01-26T21:25:06.031Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">How is it going with you??</Text></Message><Message Date="26.01.2010" Time="22:25:44" DateTime="2010-01-26T21:25:44.187Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">doing some animation, wanna see a shot?</Text></Message><Message Date="26.01.2010" Time="22:26:15" DateTime="2010-01-26T21:26:15.437Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure.</Text></Message><Message Date="26.01.2010" Time="22:26:32" DateTime="2010-01-26T21:26:32.265Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sec, i'll upload it</Text></Message><Message Date="26.01.2010" Time="22:28:13" DateTime="2010-01-26T21:28:13.671Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">k</Text></Message><Message Date="26.01.2010" Time="22:28:52" DateTime="2010-01-26T21:28:52.187Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://shcmack.site90.com/Shot%203.mov</Text></Message><Message Date="26.01.2010" Time="22:33:39" DateTime="2010-01-26T21:33:39.718Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">WOW dude!!!</Text></Message><Message Date="26.01.2010" Time="22:33:52" DateTime="2010-01-26T21:33:52.375Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i really like what you are doing with your animations.</Text></Message><Message Date="26.01.2010" Time="22:34:18" DateTime="2010-01-26T21:34:18.875Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Thats is so cool!!!</Text></Message><Message Date="26.01.2010" Time="22:34:50" DateTime="2010-01-26T21:34:50.500Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool, thanks! :D</Text></Message><Message Date="26.01.2010" Time="22:35:28" DateTime="2010-01-26T21:35:28.656Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Ok i see your getting better at what you are doing so i need to start improving on me models.</Text></Message><Message Date="26.01.2010" Time="22:36:10" DateTime="2010-01-26T21:36:10.968Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe, i spend a lot of time with 3d at the time, that helps :)</Text></Message><Message Date="26.01.2010" Time="22:37:04" DateTime="2010-01-26T21:37:04.140Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i need to get back into puting in alot of hours as i use to do.</Text></Message><Message Date="26.01.2010" Time="22:38:06" DateTime="2010-01-26T21:38:06.078Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah :) btw, you don't happen to know of a song that might fit to the clips? got two or three more clips that i'm gonna merge together</Text></Message><Message Date="26.01.2010" Time="22:38:50" DateTime="2010-01-26T21:38:50.156Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hmm i may i have alot of songs also some songs i want to use on a scene i hope to do one day.</Text></Message><Message Date="26.01.2010" Time="22:39:23" DateTime="2010-01-26T21:39:23.578Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">me too, but noone fits this animation - thought about "John Frusciante - Murderers", but it's a bit too dull</Text></Message><Message Date="26.01.2010" Time="22:40:46" DateTime="2010-01-26T21:40:46.656Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ahh i see.. do you play alot of toney hawk games??</Text></Message><Message Date="26.01.2010" Time="22:41:28" DateTime="2010-01-26T21:41:28.093Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i've played 1,2 and three - many of the songs could be cool, but everyone's heard them a million times from before</Text></Message><Message Date="26.01.2010" Time="22:43:12" DateTime="2010-01-26T21:43:12.343Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ahh i see.</Text></Message><Message Date="26.01.2010" Time="22:43:37" DateTime="2010-01-26T21:43:37.796Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have not played any of thoes games so i'm not really sure whats te best songs for it.</Text></Message><Message Date="26.01.2010" Time="22:44:53" DateTime="2010-01-26T21:44:53.484Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">tell me if you come up with a song that could fit to it :)</Text></Message><Message Date="26.01.2010" Time="22:45:23" DateTime="2010-01-26T21:45:23.890Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i will</Text></Message><Message Date="26.01.2010" Time="22:47:24" DateTime="2010-01-26T21:47:24.000Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">keep me posted on any updates i would like to see the extra animations you do on it.</Text></Message><Message Date="26.01.2010" Time="22:47:35" DateTime="2010-01-26T21:47:35.437Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">alright, will do :)</Text></Message><Message Date="26.01.2010" Time="22:52:08" DateTime="2010-01-26T21:52:08.312Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok i found a song that goes with the clip you post.</Text></Message><Message Date="26.01.2010" Time="22:52:31" DateTime="2010-01-26T21:52:31.328Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but it works out with this clip</Text></Message><Message Date="26.01.2010" Time="22:53:27" DateTime="2010-01-26T21:53:27.765Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it doesn't need to match with the clipping and action, just something that works with it :P i don't expect to find a song that match the action :P but what song did you have in mind?</Text></Message><Message Date="26.01.2010" Time="22:53:28" DateTime="2010-01-26T21:53:28.109Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">wow this songs really gos well with the clip</Text></Message><Message Date="26.01.2010" Time="22:54:05" DateTime="2010-01-26T21:54:05.937Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i understand, but this clip match the action in this clip.</Text></Message><Message Date="26.01.2010" Time="22:54:26" DateTime="2010-01-26T21:54:26.171Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it is not a fast pase song, but it really fits this clip.</Text></Message><Message Date="26.01.2010" Time="22:54:44" DateTime="2010-01-26T21:54:44.515Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">now i'm curious :)</Text></Message><Message Date="26.01.2010" Time="22:54:49" DateTime="2010-01-26T21:54:49.984Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but it will be hard to get a complete song if your not complete the full animation.</Text></Message><Message Date="26.01.2010" Time="22:54:52" DateTime="2010-01-26T21:54:52.984Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe</Text></Message><Message Date="26.01.2010" Time="22:54:57" DateTime="2010-01-26T21:54:57.953Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i really like it</Text></Message><Message Date="26.01.2010" Time="22:55:21" DateTime="2010-01-26T21:55:21.859Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">now if i can compose both your clip and this song i can send you a cool edit of it.</Text></Message><Message Date="26.01.2010" Time="22:56:07" DateTime="2010-01-26T21:56:07.046Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure, but i could just download it and see for myself? :P</Text></Message><Message Date="26.01.2010" Time="22:58:35" DateTime="2010-01-26T21:58:35.796Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok let me send you the song you need to lsten to the track from 20 when the movie clip starts to 32</Text></Message><Message Date="26.01.2010" Time="22:59:17" DateTime="2010-01-26T21:59:17.421Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok</Text></Message><Invitation Date="26.01.2010" Time="22:59:29" DateTime="2010-01-26T21:59:29.578Z" SessionID="45"><From><User FriendlyName="Fox"/></From><File>Stoned Town - 01 - Wind.mp3</File><Text Style="color:#545454; ">Fox sender Stoned Town - 01 - Wind.mp3</Text></Invitation><InvitationResponse Date="26.01.2010" Time="23:03:01" DateTime="2010-01-26T22:03:01.453Z" SessionID="45"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\Stoned Town - 01 - Wind.mp3</File><Text Style="color:#545454; ">Du har mottatt C:\Documents and Settings\Administrator\Skrivebord\Stoned Town - 01 - Wind.mp3 fra Fox.</Text></InvitationResponse><Message Date="26.01.2010" Time="23:04:06" DateTime="2010-01-26T22:04:06.375Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah listen to it from 20 to 32</Text></Message><Message Date="26.01.2010" Time="23:05:10" DateTime="2010-01-26T22:05:10.218Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">pretty good match :) but i don't think it'll work with the rest of the clip :(</Text></Message><Message Date="26.01.2010" Time="23:05:39" DateTime="2010-01-26T22:05:39.062Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i know thats why i said it really match this clip, but the rest of it i dont know.</Text></Message><Message Date="26.01.2010" Time="23:06:09" DateTime="2010-01-26T22:06:09.312Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it would be best to get the whole thing complete so you have a better idea on where to go with the clip</Text></Message><Message Date="26.01.2010" Time="23:06:48" DateTime="2010-01-26T22:06:48.406Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">actually, the best would have been if i animated the clips to a song :P i'll remember that to the next animation :)</Text></Message><Message Date="26.01.2010" Time="23:07:12" DateTime="2010-01-26T22:07:12.296Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">finding a song to an animation is almost impossible</Text></Message><Message Date="26.01.2010" Time="23:07:34" DateTime="2010-01-26T22:07:34.218Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but i gotta get to bed now</Text></Message><Message Date="26.01.2010" Time="23:07:46" DateTime="2010-01-26T22:07:46.343Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'll update you when i have my next clip ready ;)</Text></Message><Message Date="26.01.2010" Time="23:07:55" DateTime="2010-01-26T22:07:55.187Z" SessionID="45"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok see you later.</Text></Message><Message Date="26.01.2010" Time="23:08:00" DateTime="2010-01-26T22:08:00.640Z" SessionID="45"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">seeya</Text></Message><Message Date="29.01.2010" Time="17:52:26" DateTime="2010-01-29T16:52:26.750Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey</Text></Message><Message Date="29.01.2010" Time="17:52:41" DateTime="2010-01-29T16:52:41.265Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i got a couple of guys on the school to make the audio for me :)</Text></Message><Message Date="29.01.2010" Time="17:53:23" DateTime="2010-01-29T16:53:23.281Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i think it turned out pretty good, take a look :)
http://shcmack.site90.com/Sk8terboi.mov</Text></Message><Message Date="29.01.2010" Time="17:53:54" DateTime="2010-01-29T16:53:54.140Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i know that there's some weird sounds when he steps to the ground, but thei're gonna fix that</Text></Message><Message Date="29.01.2010" Time="17:54:14" DateTime="2010-01-29T16:54:14.671Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and the animation is a bit laggy, and sucks on the first clip</Text></Message><Message Date="29.01.2010" Time="18:07:26" DateTime="2010-01-29T17:07:26.781Z" SessionID="46"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">hi sorry can you send me the link later. i am on my cell right now.</Text></Message><Message Date="29.01.2010" Time="18:07:39" DateTime="2010-01-29T17:07:39.203Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah ofcourse :)</Text></Message><Message Date="30.01.2010" Time="03:54:12" DateTime="2010-01-30T02:54:12.531Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">just got home myself :)</Text></Message><Message Date="30.01.2010" Time="03:54:15" DateTime="2010-01-30T02:54:15.796Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://shcmack.site90.com/Sk8terboi.mov
</Text></Message><Message Date="30.01.2010" Time="03:54:32" DateTime="2010-01-30T02:54:32.734Z" SessionID="46"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="30.01.2010" Time="03:59:28" DateTime="2010-01-30T02:59:28.593Z" SessionID="46"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hmm it is not playing a movie it just a pic</Text></Message><Message Date="30.01.2010" Time="03:59:58" DateTime="2010-01-30T02:59:58.328Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">tried to download it instead of streaming it?</Text></Message><Message Date="30.01.2010" Time="04:00:11" DateTime="2010-01-30T03:00:11.468Z" SessionID="46"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i click on the link</Text></Message><Message Date="30.01.2010" Time="04:00:28" DateTime="2010-01-30T03:00:28.343Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://shcmack.site90.com/ &gt; right click &gt; save as</Text></Message><Message Date="30.01.2010" Time="04:01:58" DateTime="2010-01-30T03:01:58.593Z" SessionID="46"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool you finished it??</Text></Message><Message Date="30.01.2010" Time="04:02:13" DateTime="2010-01-30T03:02:13.703Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">well, almost - for now atleast</Text></Message><Message Date="30.01.2010" Time="04:02:21" DateTime="2010-01-30T03:02:21.531Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">getting pretty tired of the project :P</Text></Message><Message Date="30.01.2010" Time="04:06:52" DateTime="2010-01-30T03:06:52.875Z" SessionID="46"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe </Text></Message><Message Date="30.01.2010" Time="04:06:57" DateTime="2010-01-30T03:06:57.968Z" SessionID="46"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but it is looking good.</Text></Message><Message Date="30.01.2010" Time="04:07:22" DateTime="2010-01-30T03:07:22.812Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks, atleast i've learned a lot on the project :)</Text></Message><Message Date="30.01.2010" Time="04:10:43" DateTime="2010-01-30T03:10:43.468Z" SessionID="46"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you have</Text></Message><Message Date="30.01.2010" Time="04:10:53" DateTime="2010-01-30T03:10:53.171Z" SessionID="46"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i wish i can say the same.</Text></Message><Message Date="30.01.2010" Time="04:15:04" DateTime="2010-01-30T03:15:04.781Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">all it's take is time :) but i'm gonna hit the bed</Text></Message><Message Date="30.01.2010" Time="04:15:11" DateTime="2010-01-30T03:15:11.984Z" SessionID="46"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">good nite </Text></Message><Message Date="21.02.2010" Time="17:30:11" DateTime="2010-02-21T16:30:11.312Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">what's new?</Text></Message><Message Date="21.02.2010" Time="17:35:16" DateTime="2010-02-21T16:35:16.031Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nothing actually, trying out different things like nCloth and some mel scripting</Text></Message><Message Date="21.02.2010" Time="17:35:18" DateTime="2010-02-21T16:35:18.828Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and you?</Text></Message><Message Date="21.02.2010" Time="17:36:27" DateTime="2010-02-21T16:36:27.250Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">not much trying to find out how to use mocap files with my costom rig.</Text></Message><Message Date="21.02.2010" Time="17:36:49" DateTime="2010-02-21T16:36:49.703Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">mocap just goes directly on the joints, doesn't it?</Text></Message><Message Date="21.02.2010" Time="17:37:07" DateTime="2010-02-21T16:37:07.156Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">all fk</Text></Message><Message Date="21.02.2010" Time="17:37:20" DateTime="2010-02-21T16:37:20.578Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">no. you never use mocap files</Text></Message><Message Date="21.02.2010" Time="17:37:38" DateTime="2010-02-21T16:37:38.218Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">?</Text></Message><Message Date="21.02.2010" Time="17:38:23" DateTime="2010-02-21T16:38:23.515Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">you don't know what a mocap file?</Text></Message><Message Date="21.02.2010" Time="17:38:38" DateTime="2010-02-21T16:38:38.671Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it's a file which contains motion captured data</Text></Message><Message Date="21.02.2010" Time="17:38:48" DateTime="2010-02-21T16:38:48.593Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">yeah.</Text></Message><Message Date="21.02.2010" Time="17:39:53" DateTime="2010-02-21T16:39:53.531Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so you was trying to use mocap data on your custom rig, what i tried to say was that i don't think that works, as the actual mocap data is baked directly onto the joints - atleast that's what i've been thought</Text></Message><Message Date="21.02.2010" Time="17:41:06" DateTime="2010-02-21T16:41:06.687Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">no I can use them I know, because I saw some guys do it.</Text></Message><Message Date="21.02.2010" Time="17:41:18" DateTime="2010-02-21T16:41:18.000Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">I just don't know how.</Text></Message><Message Date="21.02.2010" Time="17:41:27" DateTime="2010-02-21T16:41:27.359Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">alrighty then :) show me if you get it to work :)</Text></Message><Message Date="21.02.2010" Time="17:42:14" DateTime="2010-02-21T16:42:14.843Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">I found out how to do it last night, but I don't get it.</Text></Message><Message Date="21.02.2010" Time="17:43:27" DateTime="2010-02-21T16:43:27.234Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">well i know how to do it in max not sure about maya, but it shold be done the same way.</Text></Message><Message Date="21.02.2010" Time="17:43:38" DateTime="2010-02-21T16:43:38.953Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">do you have access to any mocap files??</Text></Message><Message Date="21.02.2010" Time="17:44:01" DateTime="2010-02-21T16:44:01.875Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i know that motion builder have some free mocap files, but i don't have it here</Text></Message><Message Date="21.02.2010" Time="17:44:35" DateTime="2010-02-21T16:44:35.062Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have alot of mocap files i found online.</Text></Message><Message Date="21.02.2010" Time="17:44:48" DateTime="2010-02-21T16:44:48.281Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">about 400mb woth of mocap files.</Text></Message><Message Date="21.02.2010" Time="17:45:27" DateTime="2010-02-21T16:45:27.546Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i send a friend who use lightwave and creates his own rigs and he was able to apply them to his rig.</Text></Message><Message Date="21.02.2010" Time="17:45:40" DateTime="2010-02-21T16:45:40.093Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">he is really good at using lightwave animation tools.</Text></Message><Message Date="21.02.2010" Time="17:45:49" DateTime="2010-02-21T16:45:49.765Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I just wish i was that good.</Text></Message><Message Date="21.02.2010" Time="17:45:56" DateTime="2010-02-21T16:45:56.921Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">want to see what he did with some of the files??</Text></Message><Message Date="21.02.2010" Time="17:46:27" DateTime="2010-02-21T16:46:27.703Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure :)</Text></Message><Message Date="21.02.2010" Time="17:46:53" DateTime="2010-02-21T16:46:53.062Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok let me see if i can find it.</Text></Message><Invitation Date="21.02.2010" Time="17:47:33" DateTime="2010-02-21T16:47:33.609Z" SessionID="47"><From><User FriendlyName="Fox"/></From><File>Dancing test.WMV</File><Text Style="color:#545454; ">Fox sender Dancing test.WMV</Text></Invitation><Message Date="21.02.2010" Time="17:51:33" DateTime="2010-02-21T16:51:33.609Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it is a female model and she is topless</Text></Message><Message Date="21.02.2010" Time="17:51:41" DateTime="2010-02-21T16:51:41.625Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">just to warn you.</Text></Message><InvitationResponse Date="21.02.2010" Time="17:51:46" DateTime="2010-02-21T16:51:46.234Z" SessionID="47"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\Dancing test.WMV</File><Text Style="color:#545454; ">Du har mottatt C:\Documents and Settings\Administrator\Skrivebord\Dancing test.WMV fra Fox.</Text></InvitationResponse><Message Date="21.02.2010" Time="17:51:46" DateTime="2010-02-21T16:51:46.750Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">lol</Text></Message><Invitation Date="21.02.2010" Time="17:51:58" DateTime="2010-02-21T16:51:58.656Z" SessionID="47"><From><User FriendlyName="Fox"/></From><File>Jump Test.WMV</File><Text Style="color:#545454; ">Fox sender Jump Test.WMV</Text></Invitation><Message Date="21.02.2010" Time="17:52:09" DateTime="2010-02-21T16:52:09.937Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">here is another used with the same model</Text></Message><Message Date="21.02.2010" Time="17:52:24" DateTime="2010-02-21T16:52:24.500Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hah, cool!</Text></Message><Message Date="21.02.2010" Time="17:52:41" DateTime="2010-02-21T16:52:41.500Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah he did thoes with the mocap files i have.</Text></Message><Message Date="21.02.2010" Time="17:52:54" DateTime="2010-02-21T16:52:54.937Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">he dod some others but i dont have them anymoe one was a fight scene.</Text></Message><Message Date="21.02.2010" Time="17:53:00" DateTime="2010-02-21T16:53:00.875Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but what kind of things did he change?</Text></Message><Message Date="21.02.2010" Time="17:53:28" DateTime="2010-02-21T16:53:28.000Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">he did not change anything he just map the file to his rig and it worked.</Text></Message><Message Date="21.02.2010" Time="17:53:45" DateTime="2010-02-21T16:53:45.593Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i dont know the details, becasue if i did i would do the same thing.</Text></Message><InvitationResponse Date="21.02.2010" Time="17:53:50" DateTime="2010-02-21T16:53:50.140Z" SessionID="47"><From><User FriendlyName="Fox"/></From><File>C:\Documents and Settings\Administrator\Skrivebord\Jump Test.WMV</File><Text Style="color:#545454; ">Du har mottatt C:\Documents and Settings\Administrator\Skrivebord\Jump Test.WMV fra Fox.</Text></InvitationResponse><Message Date="21.02.2010" Time="17:54:36" DateTime="2010-02-21T16:54:36.578Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">But i was able ot use max biped to add mocp data to a biped rig in max, but i dont know how to do it with my costom rig, and i know it can be donw.</Text></Message><Message Date="21.02.2010" Time="17:55:31" DateTime="2010-02-21T16:55:31.112Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i don't see how that can work at all, but i guess it does huh</Text></Message><Message Date="21.02.2010" Time="17:56:11" DateTime="2010-02-21T16:56:11.190Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah it works.</Text></Message><Message Date="21.02.2010" Time="17:56:24" DateTime="2010-02-21T16:56:24.284Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you shoudl look it up for maya and see if you can.</Text></Message><Message Date="21.02.2010" Time="17:56:38" DateTime="2010-02-21T16:56:38.174Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i mean how you can do it not if you can.</Text></Message><Message Date="21.02.2010" Time="18:03:14" DateTime="2010-02-21T17:03:14.549Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have seen some really cool stuff with nclothes.</Text></Message><Message Date="21.02.2010" Time="18:03:53" DateTime="2010-02-21T17:03:53.799Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">can you send me one oof you rigs you save in maya i am going ot install maya and see if i can add a mocap file to it.</Text></Message><Message Date="21.02.2010" Time="18:04:23" DateTime="2010-02-21T17:04:23.018Z" SessionID="47"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm on my laptop, don't have any of my stuff here :/</Text></Message><Message Date="21.02.2010" Time="18:07:02" DateTime="2010-02-21T17:07:02.159Z" SessionID="47"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ahh i see</Text></Message><Message Date="26.02.2010" Time="01:12:45" DateTime="2010-02-26T00:12:45.156Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how goes your cloth sim??</Text></Message><Message Date="26.02.2010" Time="01:13:01" DateTime="2010-02-26T00:13:01.937Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not great :P</Text></Message><Message Date="26.02.2010" Time="01:13:17" DateTime="2010-02-26T00:13:17.968Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">:(</Text></Message><Message Date="26.02.2010" Time="01:13:22" DateTime="2010-02-26T00:13:22.875Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">whats wrong with it?</Text></Message><Message Date="26.02.2010" Time="01:13:50" DateTime="2010-02-26T00:13:50.281Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i get a pretty decent result when makin e.g. a t-shirt, but when using full cloth on the ninja i get some problems</Text></Message><Message Date="26.02.2010" Time="01:14:28" DateTime="2010-02-26T00:14:28.625Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what kind of problems?</Text></Message><Message Date="26.02.2010" Time="01:14:53" DateTime="2010-02-26T00:14:53.703Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i've painted input attracts on some parts of the cloth object, but that makes everything look way to rigid, even though i've turned off the stretch/bend resistance</Text></Message><Message Date="26.02.2010" Time="01:15:02" DateTime="2010-02-26T00:15:02.437Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so i've taken a little break from ncloth</Text></Message><Message Date="26.02.2010" Time="01:16:12" DateTime="2010-02-26T00:16:12.609Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh i know what you mean i have the same problems in max and i get so srtessed out that i kinda give up on it.</Text></Message><Message Date="26.02.2010" Time="01:16:27" DateTime="2010-02-26T00:16:27.640Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but i like what this guy did with his ncloths http://www.youtube.com/watch?v=XqXGtfI0Z4g</Text></Message><Message Date="26.02.2010" Time="01:17:52" DateTime="2010-02-26T00:17:52.687Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yea seen it</Text></Message><Message Date="26.02.2010" Time="01:18:11" DateTime="2010-02-26T00:18:11.250Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but still, that's kind of different</Text></Message><Message Date="26.02.2010" Time="01:18:22" DateTime="2010-02-26T00:18:22.812Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh ok</Text></Message><Message Date="26.02.2010" Time="01:19:24" DateTime="2010-02-26T00:19:24.468Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so what are you up to?</Text></Message><Message Date="26.02.2010" Time="01:19:30" DateTime="2010-02-26T00:19:30.312Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">his rig workflow looks really good.</Text></Message><Message Date="26.02.2010" Time="01:19:48" DateTime="2010-02-26T00:19:48.609Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">seen that new uv plugin for zbrush?</Text></Message><Message Date="26.02.2010" Time="01:19:58" DateTime="2010-02-26T00:19:58.671Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">uvmaster</Text></Message><Message Date="26.02.2010" Time="01:20:06" DateTime="2010-02-26T00:20:06.218Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i was messing with using vnc on my cell phone so i can connect to my pc on my cell when i'm not at home.</Text></Message><Message Date="26.02.2010" Time="01:20:23" DateTime="2010-02-26T00:20:23.250Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe</Text></Message><Message Date="26.02.2010" Time="01:20:25" DateTime="2010-02-26T00:20:25.187Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah it looks really good.</Text></Message><Message Date="26.02.2010" Time="01:20:33" DateTime="2010-02-26T00:20:33.468Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i want to give it a try.</Text></Message><Message Date="26.02.2010" Time="01:20:47" DateTime="2010-02-26T00:20:47.625Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">gonna look into it on monday</Text></Message><Message Date="26.02.2010" Time="01:21:46" DateTime="2010-02-26T00:21:46.609Z" SessionID="48"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool let me know what you think aout it... my friend who use maya told me you can add a mocap file to your bone rig.</Text></Message><Message Date="26.02.2010" Time="01:22:24" DateTime="2010-02-26T00:22:24.859Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you would atleast fk/ik on the whole rig</Text></Message><Message Date="26.02.2010" Time="01:22:30" DateTime="2010-02-26T00:22:30.968Z" SessionID="48"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">need fk/ik</Text></Message><Message Date="26.02.2010" Time="01:24:56" DateTime="2010-02-26T00:24:56.046Z" SessionID="49"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yes thats a must, </Text></Message><Message Date="20.03.2010" Time="17:22:45" DateTime="2010-03-20T16:22:45.656Z" SessionID="50"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how are your animations coming along?</Text></Message><Message Date="20.03.2010" Time="17:23:04" DateTime="2010-03-20T16:23:04.359Z" SessionID="50"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, havent done any animation lately</Text></Message><Message Date="20.03.2010" Time="17:23:19" DateTime="2010-03-20T16:23:19.078Z" SessionID="50"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">just been playing around with different things, mostly rigging and scripting</Text></Message><Message Date="20.03.2010" Time="17:23:30" DateTime="2010-03-20T16:23:30.187Z" SessionID="50"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="20.03.2010" Time="17:23:31" DateTime="2010-03-20T16:23:31.578Z" SessionID="50"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what about you? anything new?</Text></Message><Message Date="20.03.2010" Time="17:24:20" DateTime="2010-03-20T16:24:20.375Z" SessionID="50"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no worrk keept me way to busy to work on anything, and i am also just recoverying from a fever.</Text></Message><Message Date="20.03.2010" Time="17:24:36" DateTime="2010-03-20T16:24:36.953Z" SessionID="50"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I am trying ot see if i can do anything in 3ds max today.</Text></Message><Message Date="20.03.2010" Time="17:24:47" DateTime="2010-03-20T16:24:47.296Z" SessionID="50"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah i see, sucks :/</Text></Message><Message Date="20.03.2010" Time="17:24:59" DateTime="2010-03-20T16:24:59.843Z" SessionID="50"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="20.03.2010" Time="17:26:35" DateTime="2010-03-20T16:26:35.671Z" SessionID="50"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so have you moved on yo getting boob jiggle yet LOL!!</Text></Message><Message Date="20.03.2010" Time="17:27:27" DateTime="2010-03-20T16:27:27.234Z" SessionID="50"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I hsve been messing with trying to get a naturla looking boob jiggle for some time now, but i can nver get it.</Text></Message><Message Date="20.03.2010" Time="17:27:37" DateTime="2010-03-20T16:27:37.421Z" SessionID="50"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">lol, no :P</Text></Message><Message Date="20.03.2010" Time="17:28:07" DateTime="2010-03-20T16:28:07.328Z" SessionID="50"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I think that was the main reason why i am trying to learn to create rigs LOL!!</Text></Message><Message Date="20.03.2010" Time="17:28:40" DateTime="2010-03-20T16:28:40.656Z" SessionID="50"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">haha :P but gotta go</Text></Message><Message Date="20.03.2010" Time="17:28:52" DateTime="2010-03-20T16:28:52.421Z" SessionID="50"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok see you later.</Text></Message><Message Date="20.03.2010" Time="17:28:56" DateTime="2010-03-20T16:28:56.937Z" SessionID="50"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yea, cya</Text></Message><Message Date="19.04.2010" Time="03:19:32" DateTime="2010-04-19T01:19:32.640Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">what's new?</Text></Message><Message Date="19.04.2010" Time="03:20:34" DateTime="2010-04-19T01:20:34.281Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, doing some modeling for a short movie i'm gonna make</Text></Message><Message Date="19.04.2010" Time="03:20:46" DateTime="2010-04-19T01:20:46.406Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and u?</Text></Message><Message Date="19.04.2010" Time="03:21:04" DateTime="2010-04-19T01:21:04.718Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">in the hospital.</Text></Message><Message Date="19.04.2010" Time="03:21:26" DateTime="2010-04-19T01:21:26.343Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what are you doing in the hospital?!</Text></Message><Message Date="19.04.2010" Time="03:22:47" DateTime="2010-04-19T01:22:47.703Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">there was a problem with the an infection where my appendix was removed</Text></Message><Message Date="19.04.2010" Time="03:23:16" DateTime="2010-04-19T01:23:16.421Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">what is the cf movie about?</Text></Message><Message Date="19.04.2010" Time="03:23:55" DateTime="2010-04-19T01:23:55.625Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">a friend of mine just had to remove his appendix too, couple of weeks ago</Text></Message><Message Date="19.04.2010" Time="03:24:44" DateTime="2010-04-19T01:24:44.515Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i've made a poor storyboard here:
http://ideblogg.no/jorn/2010/04/12/storyboard/</Text></Message><Message Date="19.04.2010" Time="03:24:48" DateTime="2010-04-19T01:24:48.593Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">yeah it really sucks when you have to get it removed</Text></Message><Message Date="19.04.2010" Time="03:25:08" DateTime="2010-04-19T01:25:08.734Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">was it painfull before it was removed?</Text></Message><Message Date="19.04.2010" Time="03:25:44" DateTime="2010-04-19T01:25:44.984Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">yeah. very painful.</Text></Message><Message Date="19.04.2010" Time="03:26:45" DateTime="2010-04-19T01:26:45.906Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that's what my friend said too, he felt like he was going to die :/</Text></Message><Message Date="19.04.2010" Time="03:27:28" DateTime="2010-04-19T01:27:28.203Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">yeah... dude your story board is not bad.</Text></Message><Message Date="19.04.2010" Time="03:28:24" DateTime="2010-04-19T01:28:24.687Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the poses arent quite right, and i havent got the facial expressions right, but i have it in my head :)</Text></Message><Message Date="19.04.2010" Time="03:28:46" DateTime="2010-04-19T01:28:46.921Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">I wish I had access to my desktop so I can complete my story, and move onto the story board.</Text></Message><Message Date="19.04.2010" Time="03:29:23" DateTime="2010-04-19T01:29:23.750Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">you don't need any of that detail</Text></Message><Message Date="19.04.2010" Time="03:29:39" DateTime="2010-04-19T01:29:39.578Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">it is only a story board</Text></Message><Message Date="19.04.2010" Time="03:30:07" DateTime="2010-04-19T01:30:07.781Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">the final models is where you have to think about.</Text></Message><Message Date="19.04.2010" Time="03:30:15" DateTime="2010-04-19T01:30:15.937Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, when will you be out of the hospital?</Text></Message><Message Date="19.04.2010" Time="03:30:26" DateTime="2010-04-19T01:30:26.906Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">here's a test of the environment:
http://shcmack.site90.com/hus_test4.jpg</Text></Message><Message Date="19.04.2010" Time="03:30:46" DateTime="2010-04-19T01:30:46.734Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">tring to go for a "cartoon'ish" style</Text></Message><Message Date="19.04.2010" Time="03:31:41" DateTime="2010-04-19T01:31:41.156Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">I don't know yet. the doctor has not told me when, but I hope it will be tomorrow.</Text></Message><Message Date="19.04.2010" Time="03:34:09" DateTime="2010-04-19T01:34:09.078Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">wow I like.</Text></Message><Message Date="19.04.2010" Time="03:34:53" DateTime="2010-04-19T01:34:53.609Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks :)</Text></Message><Message Date="19.04.2010" Time="03:34:55" DateTime="2010-04-19T01:34:55.375Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">you have reached far with the modeling of the environment.</Text></Message><Message Date="19.04.2010" Time="03:35:36" DateTime="2010-04-19T01:35:36.328Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">ireally need to finish the girl for the story</Text></Message><Message Date="19.04.2010" Time="03:36:22" DateTime="2010-04-19T01:36:22.906Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">she is by far the most difficult part for me.</Text></Message><Message Date="19.04.2010" Time="03:36:46" DateTime="2010-04-19T01:36:46.656Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, organic modeling is hard as hell</Text></Message><Message Date="19.04.2010" Time="03:37:02" DateTime="2010-04-19T01:37:02.968Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">by the way, stupid question - but what's the english word for this?
http://www.kulturminneaaret2009.no/om_kulturminnearet_2009/for-presse-1/bildemappe-for-presse/kumlokk-%20foto%20Bjorn%20V.%20Johansen.jpg/image_preview</Text></Message><Message Date="19.04.2010" Time="03:37:24" DateTime="2010-04-19T01:37:24.625Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">I am also trying to shoot for the details you saw in that video.</Text></Message><Message Date="19.04.2010" Time="03:37:54" DateTime="2010-04-19T01:37:54.156Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i think the lighting will be pretty hard too</Text></Message><Message Date="19.04.2010" Time="03:38:28" DateTime="2010-04-19T01:38:28.843Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">oh it is called a manhole cover</Text></Message><Message Date="19.04.2010" Time="03:38:38" DateTime="2010-04-19T01:38:38.609Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah thanks! :)</Text></Message><Message Date="19.04.2010" Time="03:39:21" DateTime="2010-04-19T01:39:21.953Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">NP</Text></Message><Message Date="19.04.2010" Time="03:44:10" DateTime="2010-04-19T01:44:10.031Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">you model the manhole cover?</Text></Message><Message Date="19.04.2010" Time="03:44:30" DateTime="2010-04-19T01:44:30.109Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nah just using a texture with a bump map i think :P</Text></Message><Message Date="19.04.2010" Time="03:44:50" DateTime="2010-04-19T01:44:50.953Z" SessionID="51"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">a bit easier</Text></Message><Message Date="19.04.2010" Time="03:47:33" DateTime="2010-04-19T01:47:33.765Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">yeah that should work fine.</Text></Message><Message Date="19.04.2010" Time="03:50:55" DateTime="2010-04-19T01:50:55.203Z" SessionID="51"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">I have to model a forest for the story I am working on.</Text></Message><Message Date="24.04.2010" Time="22:44:21" DateTime="2010-04-24T20:44:21.640Z" SessionID="52"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">how goes your projecj?</Text></Message><Message Date="24.04.2010" Time="22:44:49" DateTime="2010-04-24T20:44:49.500Z" SessionID="52"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, have been banging my head in the wall lately because of some rigging issues :P</Text></Message><Message Date="24.04.2010" Time="22:44:56" DateTime="2010-04-24T20:44:56.812Z" SessionID="52"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nd you?</Text></Message><Message Date="24.04.2010" Time="22:45:59" DateTime="2010-04-24T20:45:59.437Z" SessionID="52"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">I am banging my head on the wall trying to complete my story.</Text></Message><Message Date="24.04.2010" Time="22:46:08" DateTime="2010-04-24T20:46:08.625Z" SessionID="52"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">heheh</Text></Message><Message Date="24.04.2010" Time="22:46:13" DateTime="2010-04-24T20:46:13.234Z" SessionID="52"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">isn't easy :P</Text></Message><Message Date="24.04.2010" Time="22:47:25" DateTime="2010-04-24T20:47:25.656Z" SessionID="52"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">yeah tell me about it. as hard as I may try I just can't seem to get it.</Text></Message><Message Date="24.04.2010" Time="22:48:27" DateTime="2010-04-24T20:48:27.890Z" SessionID="52"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">try to get some inspiration from short-films or something, or music.. that's what i usually do</Text></Message><Message Date="24.04.2010" Time="22:49:42" DateTime="2010-04-24T20:49:42.468Z" SessionID="52"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">yeah I am listening to a song that inspired me to write the story.</Text></Message><Message Date="14.06.2010" Time="19:01:26" DateTime="2010-06-14T17:01:26.484Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey whats new??</Text></Message><Message Date="14.06.2010" Time="19:01:49" DateTime="2010-06-14T17:01:49.546Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">any thing new to report on your project??</Text></Message><Message Date="14.06.2010" Time="19:02:00" DateTime="2010-06-14T17:02:00.765Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, nothing really.. i'm done with my project for now :)</Text></Message><Message Date="14.06.2010" Time="19:02:11" DateTime="2010-06-14T17:02:11.109Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="14.06.2010" Time="19:02:22" DateTime="2010-06-14T17:02:22.328Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://vimeo.com/12277504</Text></Message><Message Date="14.06.2010" Time="19:02:46" DateTime="2010-06-14T17:02:46.171Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i am just modeling small parts of my project until i can work out the story</Text></Message><Message Date="14.06.2010" Time="19:03:19" DateTime="2010-06-14T17:03:19.468Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sounds like a good idea, modeling props takes alot of time</Text></Message><Message Date="14.06.2010" Time="19:03:42" DateTime="2010-06-14T17:03:42.625Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah so it is best if i get stated on them now</Text></Message><Message Date="14.06.2010" Time="19:03:44" DateTime="2010-06-14T17:03:44.671Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">didn't have time for that with my project, should have had some trashcans etc etc</Text></Message><Message Date="14.06.2010" Time="19:03:47" DateTime="2010-06-14T17:03:47.687Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">while i work out the story.</Text></Message><Message Date="14.06.2010" Time="19:03:57" DateTime="2010-06-14T17:03:57.859Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="14.06.2010" Time="19:04:09" DateTime="2010-06-14T17:04:09.968Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">add more detailes to the scene</Text></Message><Message Date="14.06.2010" Time="19:06:03" DateTime="2010-06-14T17:06:03.453Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="14.06.2010" Time="19:06:38" DateTime="2010-06-14T17:06:38.390Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">talking about props, wanna see a project one in my class made?</Text></Message><Message Date="14.06.2010" Time="19:07:10" DateTime="2010-06-14T17:07:10.656Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">trying to do a grass feild, and some grass like this </Text></Message><Message Date="14.06.2010" Time="19:07:27" DateTime="2010-06-14T17:07:27.578Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ops, x'ed it out</Text></Message><Message Date="14.06.2010" Time="19:07:48" DateTime="2010-06-14T17:07:48.640Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you dont have to save it you can just check it out in the window </Text></Message><Message Date="14.06.2010" Time="19:08:16" DateTime="2010-06-14T17:08:16.375Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the fight scene will take place on something like this </Text></Message><Message Date="14.06.2010" Time="19:08:21" DateTime="2010-06-14T17:08:21.250Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">are you gonna go closeup on the grass?</Text></Message><Message Date="14.06.2010" Time="19:08:32" DateTime="2010-06-14T17:08:32.156Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">some what</Text></Message><Message Date="14.06.2010" Time="19:08:56" DateTime="2010-06-14T17:08:56.921Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the cam will pan around teh lead girl from her feet to her head</Text></Message><Message Date="14.06.2010" Time="19:09:04" DateTime="2010-06-14T17:09:04.375Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so i will get a clost up of the grass</Text></Message><Message Date="14.06.2010" Time="19:09:26" DateTime="2010-06-14T17:09:26.875Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">dunno with max, but i guess maya fur would work fine if you were using maya :P</Text></Message><Message Date="14.06.2010" Time="19:09:42" DateTime="2010-06-14T17:09:42.578Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">trying to find a way to move from the ground to that hill side you see in the last picture</Text></Message><Message Date="14.06.2010" Time="19:10:03" DateTime="2010-06-14T17:10:03.500Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah in max i can use fur also</Text></Message><Message Date="14.06.2010" Time="19:10:15" DateTime="2010-06-14T17:10:15.390Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have been reading up on it and i think i can do it.</Text></Message><Message Date="14.06.2010" Time="19:10:36" DateTime="2010-06-14T17:10:36.781Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">also want to do some leavs and grass blowing in the wind.</Text></Message><Message Date="14.06.2010" Time="19:10:58" DateTime="2010-06-14T17:10:58.437Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I hope i'm not pushing out too much.</Text></Message><Message Date="14.06.2010" Time="19:11:32" DateTime="2010-06-14T17:11:32.046Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah,  like alot of work</Text></Message><Message Date="14.06.2010" Time="19:11:42" DateTime="2010-06-14T17:11:42.531Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sound like alot of work</Text></Message><Message Date="14.06.2010" Time="19:12:11" DateTime="2010-06-14T17:12:11.484Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah it looks like it also hehe!!! but i want to do something as good as that short flim i linked you to.</Text></Message><Message Date="14.06.2010" Time="19:12:29" DateTime="2010-06-14T17:12:29.890Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and if i dont puch what i know now i dont think i will ever do it.</Text></Message><Message Date="14.06.2010" Time="19:13:02" DateTime="2010-06-14T17:13:02.125Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how much time are you planning to use?</Text></Message><Message Date="14.06.2010" Time="19:13:16" DateTime="2010-06-14T17:13:16.625Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">as much as i have.</Text></Message><Message Date="14.06.2010" Time="19:13:43" DateTime="2010-06-14T17:13:43.578Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">this is my first reall big project so i want to really put myself into it.</Text></Message><Message Date="14.06.2010" Time="19:14:01" DateTime="2010-06-14T17:14:01.281Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="14.06.2010" Time="19:14:03" DateTime="2010-06-14T17:14:03.000Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="14.06.2010" Time="19:14:38" DateTime="2010-06-14T17:14:38.515Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I dont know if you ever have any plans to do something this huge or like that short i linked you to.</Text></Message><Message Date="14.06.2010" Time="19:15:16" DateTime="2010-06-14T17:15:16.140Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">realism isn't my thing :P it's too damn hard considering the lighting and texturing for my part</Text></Message><Message Date="14.06.2010" Time="19:15:27" DateTime="2010-06-14T17:15:27.312Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i suck at lighting</Text></Message><Message Date="14.06.2010" Time="19:15:31" DateTime="2010-06-14T17:15:31.312Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and rendering</Text></Message><Message Date="14.06.2010" Time="19:15:39" DateTime="2010-06-14T17:15:39.796Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">me too</Text></Message><Message Date="14.06.2010" Time="19:15:59" DateTime="2010-06-14T17:15:59.281Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but this is a way to get better at it if i do something that really needs it.</Text></Message><Message Date="14.06.2010" Time="19:16:09" DateTime="2010-06-14T17:16:09.484Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">true</Text></Message><Message Date="14.06.2010" Time="19:16:25" DateTime="2010-06-14T17:16:25.421Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i just want to push myself.</Text></Message><Message Date="14.06.2010" Time="19:16:55" DateTime="2010-06-14T17:16:55.859Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have not done that since i stated learning to create 3d models so i guess this is the best time to do so.</Text></Message><Message Date="14.06.2010" Time="19:17:13" DateTime="2010-06-14T17:17:13.453Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Sorry i know your busy so i'm will leave you just wanted to catch up.</Text></Message><Message Date="14.06.2010" Time="19:18:28" DateTime="2010-06-14T17:18:28.921Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it's cool to finish project when you've really gone ahead of yourselves too, there's gonne be times when you just want to quit though</Text></Message><Message Date="14.06.2010" Time="19:18:38" DateTime="2010-06-14T17:18:38.453Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe, no problem, i can multitask ;)</Text></Message><Message Date="14.06.2010" Time="19:19:40" DateTime="2010-06-14T17:19:40.671Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i know that feeling i have stated work on alot of model i just lost intress in, but this one i hope is different.</Text></Message><Message Date="14.06.2010" Time="19:20:16" DateTime="2010-06-14T17:20:16.140Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">This short has been in my head for far too long so i need to get it out there.</Text></Message><Message Date="14.06.2010" Time="19:20:52" DateTime="2010-06-14T17:20:52.156Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="14.06.2010" Time="19:21:08" DateTime="2010-06-14T17:21:08.546Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">So what will be your next project??</Text></Message><Message Date="14.06.2010" Time="19:22:08" DateTime="2010-06-14T17:22:08.000Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">actually i'm not sure, i think i just wanna to a fight scene between two characters or something</Text></Message><Message Date="14.06.2010" Time="19:22:13" DateTime="2010-06-14T17:22:13.296Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">havend decided yet</Text></Message><Message Date="14.06.2010" Time="19:22:20" DateTime="2010-06-14T17:22:20.703Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i am trying to get another core i7 pc here to help with rendering when i need to render everthing</Text></Message><Message Date="14.06.2010" Time="19:22:35" DateTime="2010-06-14T17:22:35.218Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="14.06.2010" Time="19:22:35" DateTime="2010-06-14T17:22:35.312Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah yeah, rendering sucks bigtime</Text></Message><Message Date="14.06.2010" Time="19:22:46" DateTime="2010-06-14T17:22:46.812Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe</Text></Message><Message Date="14.06.2010" Time="19:23:04" DateTime="2010-06-14T17:23:04.390Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have a core i7 pc here and another one would help alot</Text></Message><Message Date="14.06.2010" Time="19:23:21" DateTime="2010-06-14T17:23:21.484Z" SessionID="53"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="14.06.2010" Time="19:24:02" DateTime="2010-06-14T17:24:02.859Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah if i buy this new pc i will have 2 core i7, and 2 amd cpus to help out with render.</Text></Message><Message Date="14.06.2010" Time="19:24:18" DateTime="2010-06-14T17:24:18.359Z" SessionID="53"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not alot but it will be a big help</Text></Message><Message Date="13.08.2010" Time="14:25:59" DateTime="2010-08-13T12:25:59.423Z" SessionID="54"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">hey what's new?</Text></Message><Message Date="13.08.2010" Time="16:20:18" DateTime="2010-08-13T14:20:18.782Z" SessionID="55"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">what's new?</Text></Message><Message Date="29.08.2010" Time="04:35:36" DateTime="2010-08-29T02:35:36.089Z" SessionID="56"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">hey what's new?</Text></Message><Message Date="19.09.2010" Time="15:02:57" DateTime="2010-09-19T13:02:57.942Z" SessionID="57"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">are you working on any new 3d models?</Text></Message><Message Date="19.09.2010" Time="15:03:15" DateTime="2010-09-19T13:03:15.623Z" SessionID="57"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, no not really</Text></Message><Message Date="19.09.2010" Time="15:03:19" DateTime="2010-09-19T13:03:19.861Z" SessionID="57"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what about you</Text></Message><Message Date="19.09.2010" Time="15:04:06" DateTime="2010-09-19T13:04:06.004Z" SessionID="57"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I am trying too, but so busy with work. I finally have a day home, and i'm trying to get some work done.</Text></Message><Message Date="19.09.2010" Time="15:04:57" DateTime="2010-09-19T13:04:57.286Z" SessionID="57"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i see, cool :) started on something new?</Text></Message><Message Date="19.09.2010" Time="15:05:42" DateTime="2010-09-19T13:05:42.442Z" SessionID="57"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">trying to work on the same project i told you about.</Text></Message><Message Date="19.09.2010" Time="15:06:20" DateTime="2010-09-19T13:06:20.005Z" SessionID="57"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, any progress on that one since last time?</Text></Message><Message Date="19.09.2010" Time="15:06:52" DateTime="2010-09-19T13:06:52.091Z" SessionID="57"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no i keep trying to work on it, but work gets in the way.</Text></Message><Message Date="19.09.2010" Time="15:07:19" DateTime="2010-09-19T13:07:19.632Z" SessionID="57"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">arriving home late, and waking up early for work really kills my time.</Text></Message><Message Date="19.09.2010" Time="15:07:45" DateTime="2010-09-19T13:07:45.743Z" SessionID="57"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but i still want to complete this project.</Text></Message><Message Date="19.09.2010" Time="15:08:13" DateTime="2010-09-19T13:08:13.366Z" SessionID="57"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i know what you mean</Text></Message><Message Date="19.09.2010" Time="15:08:24" DateTime="2010-09-19T13:08:24.199Z" SessionID="57"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you should :)</Text></Message><Message Date="19.09.2010" Time="15:08:47" DateTime="2010-09-19T13:08:47.577Z" SessionID="57"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i plan too. I just need the time to work on it.</Text></Message><Message Date="19.09.2010" Time="15:27:48" DateTime="2010-09-19T13:27:48.630Z" SessionID="58"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">So are you on a long break from 3d riging and modeling?</Text></Message><Message Date="19.09.2010" Time="15:28:31" DateTime="2010-09-19T13:28:31.575Z" SessionID="58"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not at all, i'm actually scripting right now.. that's the thing, lately i've just been scripting and rigging</Text></Message><Message Date="19.09.2010" Time="15:28:39" DateTime="2010-09-19T13:28:39.557Z" SessionID="58"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and done a bit animation</Text></Message><Message Date="19.09.2010" Time="15:29:19" DateTime="2010-09-19T13:29:19.324Z" SessionID="58"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh cool are you working mel scripts?</Text></Message><Message Date="19.09.2010" Time="15:29:24" DateTime="2010-09-19T13:29:24.103Z" SessionID="58"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="19.09.2010" Time="15:29:56" DateTime="2010-09-19T13:29:56.331Z" SessionID="58"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice how is that coming along??</Text></Message><Message Date="19.09.2010" Time="15:30:38" DateTime="2010-09-19T13:30:38.470Z" SessionID="58"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">pretty goot actually, i've made myself a neat collection of usefull scripts :) that's what's so amazing about it, you can automate literally anything :)</Text></Message><Message Date="19.09.2010" Time="15:31:11" DateTime="2010-09-19T13:31:11.336Z" SessionID="58"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm working on a set of rigging-scripts at the moment</Text></Message><Message Date="19.09.2010" Time="15:31:13" DateTime="2010-09-19T13:31:13.688Z" SessionID="58"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oops</Text></Message><Message Date="19.09.2010" Time="15:31:28" DateTime="2010-09-19T13:31:28.398Z" SessionID="58"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i need to get into learning more max scripts.</Text></Message><Message Date="19.09.2010" Time="15:31:44" DateTime="2010-09-19T13:31:44.071Z" SessionID="58"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I really like it, but i just had not spend alot of time messing with it.</Text></Message><Message Date="19.09.2010" Time="15:31:58" DateTime="2010-09-19T13:31:58.935Z" SessionID="58"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you should :) totally worth it</Text></Message><Message Date="19.09.2010" Time="15:32:06" DateTime="2010-09-19T13:32:06.020Z" SessionID="58"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I am trying learn modeling more.</Text></Message><Message Date="19.09.2010" Time="15:32:07" DateTime="2010-09-19T13:32:07.478Z" SessionID="58"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="19.09.2010" Time="15:33:32" DateTime="2010-09-19T13:33:32.582Z" SessionID="58"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have a cool site you may want to check out.. let me see if i can find the link. It is with a we page to a guy who does mel scripts, and some of the cool stuff he has.</Text></Message><Message Date="01.11.2010" Time="16:07:11" DateTime="2010-11-01T15:07:11.939Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey how is it going?? working on any new 3d models or rigs?</Text></Message><Message Date="01.11.2010" Time="16:17:07" DateTime="2010-11-01T15:17:07.033Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey man, well, i'm doing a littlebit of everything</Text></Message><Message Date="01.11.2010" Time="16:17:14" DateTime="2010-11-01T15:17:14.408Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no serious projects though</Text></Message><Message Date="01.11.2010" Time="16:17:52" DateTime="2010-11-01T15:17:52.812Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe i know what you mean i'm also messing with a littlebit of everthing myseld.</Text></Message><Message Date="01.11.2010" Time="16:18:05" DateTime="2010-11-01T15:18:05.477Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">doing osme modeling rigging, and particles</Text></Message><Message Date="01.11.2010" Time="16:18:34" DateTime="2010-11-01T15:18:34.472Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I finally have my server up and running</Text></Message><Message Date="01.11.2010" Time="16:20:03" DateTime="2010-11-01T15:20:03.161Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="01.11.2010" Time="16:21:39" DateTime="2010-11-01T15:21:39.966Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yah i'm am using it for rendering and hosting my web site</Text></Message><Message Date="01.11.2010" Time="16:21:48" DateTime="2010-11-01T15:21:48.606Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">which really sucks at the moment LOL!!</Text></Message><Message Date="01.11.2010" Time="16:21:58" DateTime="2010-11-01T15:21:58.121Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe</Text></Message><Message Date="01.11.2010" Time="16:22:01" DateTime="2010-11-01T15:22:01.113Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what's the site?</Text></Message><Message Date="01.11.2010" Time="16:22:22" DateTime="2010-11-01T15:22:22.291Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the url is http://www.fox3dmodels.com</Text></Message><Message Date="01.11.2010" Time="16:22:57" DateTime="2010-11-01T15:22:57.978Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what you see on it now is just place holders i am still working on the logo to add to it, and my finish projects i plan to complete for it</Text></Message><Message Date="01.11.2010" Time="16:23:54" DateTime="2010-11-01T15:23:54.305Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Yeah i have to pc's i my room one is for my 3d modeling and the other is just for my web server and file server... i many also use it to help with rendering if it is too much for my main pc to do alone</Text></Message><Message Date="01.11.2010" Time="16:24:01" DateTime="2010-11-01T15:24:01.720Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah :)</Text></Message><Message Date="01.11.2010" Time="16:24:29" DateTime="2010-11-01T15:24:29.871Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you should do a site also to showcase your works</Text></Message><Message Date="01.11.2010" Time="16:25:55" DateTime="2010-11-01T15:25:55.641Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i've thought about it.. just never got myself to do it :P</Text></Message><Message Date="01.11.2010" Time="16:26:28" DateTime="2010-11-01T15:26:28.643Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the server seup for me is easy the web design is killing me LOL!!</Text></Message><Message Date="01.11.2010" Time="16:26:40" DateTime="2010-11-01T15:26:40.858Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe</Text></Message><Message Date="01.11.2010" Time="16:26:44" DateTime="2010-11-01T15:26:44.506Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">why not use a cms?</Text></Message><Message Date="01.11.2010" Time="16:26:54" DateTime="2010-11-01T15:26:54.618Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">like wordpress or joomla or something</Text></Message><Message Date="01.11.2010" Time="16:27:08" DateTime="2010-11-01T15:27:08.968Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i am usiong dresmwaver.</Text></Message><Message Date="01.11.2010" Time="16:27:43" DateTime="2010-11-01T15:27:43.181Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it is really cool,but like i said is the design of teh site is killing me i just cant seem to find a look on how i want it to be.</Text></Message><Message Date="01.11.2010" Time="16:27:59" DateTime="2010-11-01T15:27:59.481Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">check out www.templatemonster.com</Text></Message><Message Date="01.11.2010" Time="16:28:11" DateTime="2010-11-01T15:28:11.048Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">lots of inspiration you can get from there</Text></Message><Message Date="01.11.2010" Time="16:28:15" DateTime="2010-11-01T15:28:15.468Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool </Text></Message><Message Date="01.11.2010" Time="16:28:18" DateTime="2010-11-01T15:28:18.883Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks man</Text></Message><Message Date="01.11.2010" Time="16:31:14" DateTime="2010-11-01T15:31:14.649Z" SessionID="59"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no prob :)</Text></Message><Message Date="01.11.2010" Time="16:31:44" DateTime="2010-11-01T15:31:44.978Z" SessionID="59"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have alot of WIP's to put on the site  then finished content LOL!!</Text></Message><Message Date="19.11.2010" Time="17:31:55" DateTime="2010-11-19T16:31:55.346Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey whats new??</Text></Message><Message Date="19.11.2010" Time="17:32:18" DateTime="2010-11-19T16:32:18.347Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ey, nothing really</Text></Message><Message Date="19.11.2010" Time="17:32:40" DateTime="2010-11-19T16:32:40.346Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh ok cool</Text></Message><Message Date="19.11.2010" Time="17:33:22" DateTime="2010-11-19T16:33:22.938Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i made an animetion with my tunnel</Text></Message><Message Date="19.11.2010" Time="17:33:25" DateTime="2010-11-19T16:33:25.923Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">care to see it.</Text></Message><Message Date="19.11.2010" Time="17:33:31" DateTime="2010-11-19T16:33:31.468Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure</Text></Message><Message Date="19.11.2010" Time="17:33:47" DateTime="2010-11-19T16:33:47.584Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it is on my site,but the video is 10mb</Text></Message><Message Date="19.11.2010" Time="17:34:38" DateTime="2010-11-19T16:34:38.753Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh and this is a base mesh for a star ship i am working on http://www.fox3dmodels.com/wip/ShipTop.jpg</Text></Message><Message Date="19.11.2010" Time="17:34:51" DateTime="2010-11-19T16:34:51.256Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://www.fox3dmodels.com/wip/ShipBack.jpg</Text></Message><Message Date="19.11.2010" Time="17:35:07" DateTime="2010-11-19T16:35:07.571Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://www.fox3dmodels.com/wip/Shipside.jpg</Text></Message><Message Date="19.11.2010" Time="17:35:19" DateTime="2010-11-19T16:35:19.020Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">trying to work out the details i can add on it.</Text></Message><Message Date="19.11.2010" Time="17:35:47" DateTime="2010-11-19T16:35:47.200Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">This is the link for the video http://www.fox3dmodels.com/wip/ShipFlyBy.avi</Text></Message><Message Date="19.11.2010" Time="17:39:38" DateTime="2010-11-19T16:39:38.619Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it was just a test i know i need to clean up the animation.</Text></Message><Message Date="19.11.2010" Time="17:40:36" DateTime="2010-11-19T16:40:36.481Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">my plan is to have it fly over head of teh walkway and on the other side go to warp speed.</Text></Message><Message Date="19.11.2010" Time="17:42:48" DateTime="2010-11-19T16:42:48.908Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">looks like the ship is coming in a bit sideways</Text></Message><Message Date="19.11.2010" Time="17:43:00" DateTime="2010-11-19T16:43:00.203Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i know</Text></Message><Message Date="19.11.2010" Time="17:43:04" DateTime="2010-11-19T16:43:04.493Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and maybe a bit slow?</Text></Message><Message Date="19.11.2010" Time="17:43:06" DateTime="2010-11-19T16:43:06.278Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">like i said a test</Text></Message><Message Date="19.11.2010" Time="17:43:12" DateTime="2010-11-19T16:43:12.271Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="19.11.2010" Time="17:43:31" DateTime="2010-11-19T16:43:31.741Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i am still working on learning how to use the animation settings in max</Text></Message><Message Date="19.11.2010" Time="17:43:55" DateTime="2010-11-19T16:43:55.984Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i was trying to slow it down, but it does not look like i found the settings to do that.</Text></Message><Message Date="19.11.2010" Time="17:44:18" DateTime="2010-11-19T16:44:18.524Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you should see if there's an animation tutorial for max</Text></Message><Message Date="19.11.2010" Time="17:44:29" DateTime="2010-11-19T16:44:29.547Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hard to figure out everything yourself</Text></Message><Message Date="19.11.2010" Time="17:44:39" DateTime="2010-11-19T16:44:39.012Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">there is i just had not have the time to look at it.</Text></Message><Message Date="19.11.2010" Time="17:44:50" DateTime="2010-11-19T16:44:50.428Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i see</Text></Message><Message Date="19.11.2010" Time="17:45:05" DateTime="2010-11-19T16:45:05.086Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">still working on modeling animation will come last</Text></Message><Message Date="19.11.2010" Time="17:45:16" DateTime="2010-11-19T16:45:16.267Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what do you think about teh shape of the ship?</Text></Message><Message Date="19.11.2010" Time="17:45:39" DateTime="2010-11-19T16:45:39.456Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">does it look cool or should i go for a diffrent look?</Text></Message><Message Date="19.11.2010" Time="17:45:45" DateTime="2010-11-19T16:45:45.436Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ship</Text></Message><Message Date="19.11.2010" Time="17:47:06" DateTime="2010-11-19T16:47:06.027Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i think the shape works fine, but i don't know if i like the combination of smooth surfaces and hard edges</Text></Message><Message Date="19.11.2010" Time="17:47:27" DateTime="2010-11-19T16:47:27.500Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">used any references?</Text></Message><Message Date="19.11.2010" Time="17:47:33" DateTime="2010-11-19T16:47:33.162Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no</Text></Message><Message Date="19.11.2010" Time="17:47:39" DateTime="2010-11-19T16:47:39.531Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you should :)</Text></Message><Message Date="19.11.2010" Time="17:47:50" DateTime="2010-11-19T16:47:50.266Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">true</Text></Message><Message Date="19.11.2010" Time="17:47:59" DateTime="2010-11-19T16:47:59.691Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">get inspiration, then make your own from that</Text></Message><Message Date="19.11.2010" Time="17:48:44" DateTime="2010-11-19T16:48:44.575Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh i was loking at the ship from stargate universe</Text></Message><Message Date="19.11.2010" Time="17:48:45" DateTime="2010-11-19T16:48:45.117Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i would probably go for a lot of details, and keep it hard surface</Text></Message><Message Date="19.11.2010" Time="17:48:51" DateTime="2010-11-19T16:48:51.420Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">like steel-plates</Text></Message><Message Date="19.11.2010" Time="17:49:40" DateTime="2010-11-19T16:49:40.413Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I see.. This is my first ship so not sure where to really start with it. I made that because i wanted to see i could come up with something cool</Text></Message><Message Date="19.11.2010" Time="17:49:56" DateTime="2010-11-19T16:49:56.379Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh i also found this image let me post it on my site.</Text></Message><Message Date="19.11.2010" Time="17:50:33" DateTime="2010-11-19T16:50:33.625Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sorry i maybe holding you back form doing what ever you where doing i will show you it some other time if you're busy.</Text></Message><Message Date="19.11.2010" Time="17:51:26" DateTime="2010-11-19T16:51:26.540Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm not doing anything important, just fucking bored with a bad hangover</Text></Message><Message Date="19.11.2010" Time="17:52:01" DateTime="2010-11-19T16:52:01.103Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe oh i see party a lot last night?</Text></Message><Message Date="19.11.2010" Time="17:52:27" DateTime="2010-11-19T16:52:27.639Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://www.fox3dmodels.com/wip/flying-ship-large.jpg</Text></Message><Message Date="19.11.2010" Time="17:52:38" DateTime="2010-11-19T16:52:38.987Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">kinda, broke up with my girlfriend so i'm just getting my mind at other things :P</Text></Message><Message Date="19.11.2010" Time="17:52:40" DateTime="2010-11-19T16:52:40.720Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i like how that should looks</Text></Message><Message Date="19.11.2010" Time="17:52:48" DateTime="2010-11-19T16:52:48.317Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that was cool</Text></Message><Message Date="19.11.2010" Time="17:52:51" DateTime="2010-11-19T16:52:51.385Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ahhh i hear you.</Text></Message><Message Date="19.11.2010" Time="17:53:11" DateTime="2010-11-19T16:53:11.948Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i realyl like it so i was kinda going for something like it,but diffrent</Text></Message><Message Date="19.11.2010" Time="17:53:52" DateTime="2010-11-19T16:53:52.907Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="19.11.2010" Time="17:54:01" DateTime="2010-11-19T16:54:01.755Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">kinda looks like a boat :P</Text></Message><Message Date="19.11.2010" Time="17:54:11" DateTime="2010-11-19T16:54:11.110Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sorry when i said i did not use a refrence i did.</Text></Message><Message Date="19.11.2010" Time="17:54:14" DateTime="2010-11-19T16:54:14.840Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i use that ship</Text></Message><Message Date="19.11.2010" Time="17:54:28" DateTime="2010-11-19T16:54:28.020Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and some others to see if i can come up with my own idea</Text></Message><Message Date="19.11.2010" Time="17:54:44" DateTime="2010-11-19T16:54:44.347Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah it looks like a cross beteween a sub and a ship</Text></Message><Message Date="19.11.2010" Time="17:55:25" DateTime="2010-11-19T16:55:25.611Z" SessionID="60"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i see, there's a lot quicker to just draw it in 2d first :)</Text></Message><Message Date="19.11.2010" Time="17:56:03" DateTime="2010-11-19T16:56:03.036Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i suck at drawing :(</Text></Message><Message Date="19.11.2010" Time="17:57:27" DateTime="2010-11-19T16:57:27.053Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://www.fox3dmodels.com/wip/Sentinel_Ship_by_Ginhebi.jpg</Text></Message><Message Date="19.11.2010" Time="17:57:36" DateTime="2010-11-19T16:57:36.754Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">a few other pic i found and like</Text></Message><Message Date="19.11.2010" Time="17:58:08" DateTime="2010-11-19T16:58:08.085Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://www.fox3dmodels.com/wip/Imperial_Cruiser__by_MeckanicalMind.jpg</Text></Message><Message Date="19.11.2010" Time="17:58:13" DateTime="2010-11-19T16:58:13.530Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i really like that one</Text></Message><Message Date="19.11.2010" Time="17:58:55" DateTime="2010-11-19T16:58:55.392Z" SessionID="60"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have about 20 pic i use as a refrence.</Text></Message><Message Date="14.12.2010" Time="20:50:50" DateTime="2010-12-14T19:50:50.495Z" SessionID="61"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">hey what's new?</Text></Message><Message Date="14.12.2010" Time="21:49:17" DateTime="2010-12-14T20:49:17.858Z" SessionID="61"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey</Text></Message><Message Date="14.12.2010" Time="23:10:17" DateTime="2010-12-14T22:10:17.838Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">hi how is it going?</Text></Message><Message Date="15.12.2010" Time="00:25:54" DateTime="2010-12-14T23:25:54.930Z" SessionID="62"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hola.. well, i've had better days :P</Text></Message><Message Date="15.12.2010" Time="00:25:59" DateTime="2010-12-14T23:25:59.043Z" SessionID="62"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what about you?</Text></Message><Message Date="15.12.2010" Time="00:26:19" DateTime="2010-12-14T23:26:19.944Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">te same.</Text></Message><Message Date="15.12.2010" Time="00:26:30" DateTime="2010-12-14T23:26:30.897Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the</Text></Message><Message Date="15.12.2010" Time="00:26:46" DateTime="2010-12-14T23:26:46.823Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i finally order a new laptop.</Text></Message><Message Date="15.12.2010" Time="00:27:56" DateTime="2010-12-14T23:27:56.194Z" SessionID="62"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool, how much does a decent laptop cost in the us?</Text></Message><Message Date="15.12.2010" Time="00:28:46" DateTime="2010-12-14T23:28:46.335Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">around $500 but my laptop was not that cheap.</Text></Message><Message Date="15.12.2010" Time="00:29:13" DateTime="2010-12-14T23:29:13.985Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">the laptop i ordr is $2000</Text></Message><Message Date="15.12.2010" Time="00:29:46" DateTime="2010-12-14T23:29:46.706Z" SessionID="62"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">wow, you've ordered a beast then?</Text></Message><Message Date="15.12.2010" Time="00:29:54" DateTime="2010-12-14T23:29:54.300Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="15.12.2010" Time="00:30:00" DateTime="2010-12-14T23:30:00.093Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it has 6gb of ram</Text></Message><Message Date="15.12.2010" Time="00:30:15" DateTime="2010-12-14T23:30:15.114Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">a core i7 780 cpu</Text></Message><Message Date="15.12.2010" Time="00:30:23" DateTime="2010-12-14T23:30:23.691Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">a killer sound system</Text></Message><Message Date="15.12.2010" Time="00:30:51" DateTime="2010-12-14T23:30:51.233Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">bul-ray drive, and a 1tb hard drive</Text></Message><Message Date="15.12.2010" Time="00:31:16" DateTime="2010-12-14T23:31:16.034Z" SessionID="62"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice, so no ssd drive?</Text></Message><Message Date="15.12.2010" Time="00:31:34" DateTime="2010-12-14T23:31:34.745Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no</Text></Message><Message Date="15.12.2010" Time="00:32:13" DateTime="2010-12-14T23:32:13.511Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i dont think i need one</Text></Message><Message Date="15.12.2010" Time="00:32:35" DateTime="2010-12-14T23:32:35.825Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">this cpu will be a big help to my desktop when rendering</Text></Message><Message Date="15.12.2010" Time="00:33:36" DateTime="2010-12-14T23:33:36.897Z" SessionID="62"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, well i just loooove to run my os on ssd</Text></Message><Message Date="15.12.2010" Time="00:34:51" DateTime="2010-12-14T23:34:51.158Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">really?</Text></Message><Message Date="15.12.2010" Time="00:35:10" DateTime="2010-12-14T23:35:10.611Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">for what i do on my pc i dont think i can use ssd drives</Text></Message><Message Date="15.12.2010" Time="00:36:03" DateTime="2010-12-14T23:36:03.448Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so you only have 1 hard drive on your laptop?</Text></Message><Message Date="15.12.2010" Time="00:39:55" DateTime="2010-12-14T23:39:55.915Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Question if your rendering a very large space ship flying over head how do you get it to fly by and you can see how large it is. because it takes up all of your view, and it takes awhile to fly by.</Text></Message><Message Date="15.12.2010" Time="00:44:05" DateTime="2010-12-14T23:44:05.906Z" SessionID="62"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">well, i don't use it one my laptop, i use it on my stationary comp, then i use sata drives as storage</Text></Message><Message Date="15.12.2010" Time="00:44:19" DateTime="2010-12-14T23:44:19.921Z" SessionID="62"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hmm, you could play around with the focal length on the camera</Text></Message><Message Date="15.12.2010" Time="00:46:01" DateTime="2010-12-14T23:46:01.841Z" SessionID="62"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hmmm i will try that</Text></Message><Message Date="18.12.2010" Time="16:03:40" DateTime="2010-12-18T15:03:40.958Z" SessionID="63"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">finally my New laptop is here, and it sports the core i7 840 CPU.</Text></Message><Message Date="18.12.2010" Time="16:04:01" DateTime="2010-12-18T15:04:01.265Z" SessionID="63"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe, nice :)</Text></Message><Message Date="18.12.2010" Time="16:06:01" DateTime="2010-12-18T15:06:01.375Z" SessionID="63"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">yeah I format it last night to remove all the junk on it now I am trying to set it up the way I like.</Text></Message><Message Date="27.12.2010" Time="17:18:52" DateTime="2010-12-27T16:18:52.930Z" SessionID="64"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey what's new?</Text></Message><Message Date="02.01.2011" Time="18:27:49" DateTime="2011-01-02T17:27:49.297Z" SessionID="65"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://www.youtube.com/watch?v=hNAXVs8rydU&amp;feature=player_embedded one good reason to get kinect</Text></Message><Message Date="22.01.2011" Time="19:50:08" DateTime="2011-01-22T18:50:08.986Z" SessionID="66"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="color:windowtext; ">what's new?</Text></Message><Message Date="22.04.2011" Time="18:46:03" DateTime="2011-04-22T16:46:03.223Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey whats new?</Text></Message><Message Date="22.04.2011" Time="18:46:11" DateTime="2011-04-22T16:46:11.189Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey man</Text></Message><Message Date="22.04.2011" Time="18:46:29" DateTime="2011-04-22T16:46:29.720Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">What have you been up too?</Text></Message><Message Date="22.04.2011" Time="18:46:39" DateTime="2011-04-22T16:46:39.622Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">working on a animated short film with three other guys :)</Text></Message><Message Date="22.04.2011" Time="18:46:42" DateTime="2011-04-22T16:46:42.981Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what about you?</Text></Message><Message Date="22.04.2011" Time="18:47:03" DateTime="2011-04-22T16:47:03.782Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">trying to learn animation</Text></Message><Message Date="22.04.2011" Time="18:47:23" DateTime="2011-04-22T16:47:23.336Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">my female rig is fine, now i need to learn to animate it</Text></Message><Message Date="22.04.2011" Time="18:47:34" DateTime="2011-04-22T16:47:34.805Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="22.04.2011" Time="18:47:45" DateTime="2011-04-22T16:47:45.047Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">aslo trying to find out how i can do an FTl efftect from stargate</Text></Message><Message Date="22.04.2011" Time="18:48:01" DateTime="2011-04-22T16:48:01.189Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">check out keith lango, he has some pretty awsome tutorials on animation</Text></Message><Message Date="22.04.2011" Time="18:48:09" DateTime="2011-04-22T16:48:09.845Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">fti?</Text></Message><Message Date="22.04.2011" Time="18:48:17" DateTime="2011-04-22T16:48:17.077Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">faster then alight</Text></Message><Message Date="22.04.2011" Time="18:48:20" DateTime="2011-04-22T16:48:20.812Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">light</Text></Message><Message Date="22.04.2011" Time="18:48:44" DateTime="2011-04-22T16:48:44.961Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sorry i mean FTL</Text></Message><Message Date="22.04.2011" Time="18:49:01" DateTime="2011-04-22T16:49:01.845Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">havent seen stargate :P</Text></Message><Message Date="22.04.2011" Time="18:49:10" DateTime="2011-04-22T16:49:10.070Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">lol</Text></Message><Message Date="22.04.2011" Time="18:49:19" DateTime="2011-04-22T16:49:19.397Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it is a cool scifi show</Text></Message><Message Date="22.04.2011" Time="18:50:03" DateTime="2011-04-22T16:50:03.461Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm not that much into sci-fi, i havent even seen star wars :P</Text></Message><Message Date="22.04.2011" Time="18:50:52" DateTime="2011-04-22T16:50:52.109Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i see well it is an effeft like this .</Text></Message><Message Date="22.04.2011" Time="18:51:16" DateTime="2011-04-22T16:51:16.841Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">stargate universe destiny ftl</Text></Message><Message Date="22.04.2011" Time="18:52:15" DateTime="2011-04-22T16:52:15.443Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://www.youtube.com/watch?v=vORF98DM9XI</Text></Message><Message Date="22.04.2011" Time="18:52:19" DateTime="2011-04-22T16:52:19.881Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sorry</Text></Message><Message Date="22.04.2011" Time="18:52:34" DateTime="2011-04-22T16:52:34.885Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i see</Text></Message><Message Date="22.04.2011" Time="18:52:57" DateTime="2011-04-22T16:52:57.109Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">shouln't that be possible to make with after effects or something?</Text></Message><Message Date="22.04.2011" Time="18:53:41" DateTime="2011-04-22T16:53:41.424Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i should be able too if i knew how to use after effect heheh</Text></Message><Message Date="22.04.2011" Time="18:55:14" DateTime="2011-04-22T16:55:14.042Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i also want to do the effect with the ship in FTL and it loooks liek it is moving through a wave or something http://www.youtube.com/watch?v=wgELNVD40fA&amp;feature=related</Text></Message><Message Date="22.04.2011" Time="18:57:16" DateTime="2011-04-22T16:57:16.517Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="22.04.2011" Time="18:57:39" DateTime="2011-04-22T16:57:39.522Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i really love the effect from that show, just wish i knew how to do them.</Text></Message><Message Date="22.04.2011" Time="18:57:54" DateTime="2011-04-22T16:57:54.277Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="22.04.2011" Time="18:58:41" DateTime="2011-04-22T16:58:41.205Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">just an idea, if you could apply a smok'ish cool texture to a plane, and the just animate and deform the plane</Text></Message><Message Date="22.04.2011" Time="18:58:43" DateTime="2011-04-22T16:58:43.365Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">could work</Text></Message><Message Date="22.04.2011" Time="18:59:13" DateTime="2011-04-22T16:59:13.062Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i don't know :P</Text></Message><Message Date="22.04.2011" Time="18:59:16" DateTime="2011-04-22T16:59:16.593Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah that sould like it could.</Text></Message><Message Date="22.04.2011" Time="18:59:33" DateTime="2011-04-22T16:59:33.779Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">do you haev a link to that animation tut ?</Text></Message><Message Date="22.04.2011" Time="19:00:09" DateTime="2011-04-22T17:00:09.206Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no, long time since i've seen'em, theire not free, but i guess there's a torrent laying around somewhere if you google it</Text></Message><Message Date="22.04.2011" Time="19:00:30" DateTime="2011-04-22T17:00:30.358Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok thanks dude.</Text></Message><Message Date="22.04.2011" Time="19:01:42" DateTime="2011-04-22T17:01:42.724Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">d oyou know your way around after effects?</Text></Message><Message Date="22.04.2011" Time="19:02:23" DateTime="2011-04-22T17:02:23.174Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nah, just know the basics.. you've checked out videocopilot, right?</Text></Message><Message Date="22.04.2011" Time="19:02:36" DateTime="2011-04-22T17:02:36.243Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nope</Text></Message><Message Date="22.04.2011" Time="19:02:39" DateTime="2011-04-22T17:02:39.110Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">do it</Text></Message><Message Date="22.04.2011" Time="19:02:55" DateTime="2011-04-22T17:02:55.542Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">awesome free after effects tutorials</Text></Message><Message Date="22.04.2011" Time="19:03:18" DateTime="2011-04-22T17:03:18.814Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice i iwll check it out now</Text></Message><Message Date="22.04.2011" Time="19:03:31" DateTime="2011-04-22T17:03:31.639Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">So what is your showreel about??</Text></Message><Message Date="22.04.2011" Time="19:04:49" DateTime="2011-04-22T17:04:49.045Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">havent made myself a showreel, i'm planning to use the short-film as my showreel-material</Text></Message><Message Date="22.04.2011" Time="19:04:57" DateTime="2011-04-22T17:04:57.878Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">as i've done all the rigging, and most of the animation on it</Text></Message><Message Date="22.04.2011" Time="19:05:10" DateTime="2011-04-22T17:05:10.512Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh cool</Text></Message><Message Date="22.04.2011" Time="19:05:25" DateTime="2011-04-22T17:05:25.091Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i dont know when my show reel will be complete :(</Text></Message><Message Date="22.04.2011" Time="19:07:40" DateTime="2011-04-22T17:07:40.550Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">started on your reel yet?</Text></Message><Message Date="22.04.2011" Time="19:08:08" DateTime="2011-04-22T17:08:08.571Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">still doing a lot of modeling</Text></Message><Message Date="22.04.2011" Time="19:08:49" DateTime="2011-04-22T17:08:49.022Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i am more an 3d modeler than a animator or special effect guy.</Text></Message><Message Date="22.04.2011" Time="19:09:01" DateTime="2011-04-22T17:09:01.698Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so it is hard to jump on to the other stuff.</Text></Message><Message Date="22.04.2011" Time="19:09:58" DateTime="2011-04-22T17:09:58.072Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I am also trying to learn autocad so i can do a really cool work up of the inside of my ship.</Text></Message><Message Date="22.04.2011" Time="19:11:00" DateTime="2011-04-22T17:11:00.917Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">combining autocad with 3ds max?</Text></Message><Message Date="22.04.2011" Time="19:11:10" DateTime="2011-04-22T17:11:10.422Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">does that even work?</Text></Message><Message Date="22.04.2011" Time="19:11:11" DateTime="2011-04-22T17:11:11.208Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yup</Text></Message><Message Date="22.04.2011" Time="19:11:32" DateTime="2011-04-22T17:11:32.816Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah it does both are made by the same ppl</Text></Message><Message Date="22.04.2011" Time="19:11:51" DateTime="2011-04-22T17:11:51.359Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">so you can import what ever you do in autocad and work on it in max</Text></Message><Message Date="22.04.2011" Time="19:11:54" DateTime="2011-04-22T17:11:54.501Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i know, but what can you do with autocad that you can't do with max?</Text></Message><Message Date="22.04.2011" Time="19:12:37" DateTime="2011-04-22T17:12:37.060Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">do a deatail drawing of your blueprints for what ever you want to design.</Text></Message><Message Date="22.04.2011" Time="19:13:05" DateTime="2011-04-22T17:13:05.929Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">also i want to do a  1 to 1 scale model so i want the correct units</Text></Message><Message Date="22.04.2011" Time="19:13:13" DateTime="2011-04-22T17:13:13.775Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">autocd will help me do that.</Text></Message><Message Date="22.04.2011" Time="19:15:25" DateTime="2011-04-22T17:15:25.430Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i see</Text></Message><Message Date="22.04.2011" Time="19:17:05" DateTime="2011-04-22T17:17:05.168Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah keep me posted on your animation reel i would like to see who it turns out. i will send you link on what i have if you would like to check out what i come up with.</Text></Message><Message Date="22.04.2011" Time="19:17:49" DateTime="2011-04-22T17:17:49.414Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure, i can check out http://ideblogg.no/fortheking/ on some of the stuff, gonna wait to put up the animation until the movie is complete</Text></Message><Message Date="22.04.2011" Time="19:17:58" DateTime="2011-04-22T17:17:58.229Z" SessionID="67"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">do that</Text></Message><Message Date="22.04.2011" Time="19:18:15" DateTime="2011-04-22T17:18:15.672Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool</Text></Message><Message Date="22.04.2011" Time="19:19:09" DateTime="2011-04-22T17:19:09.476Z" SessionID="67"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice smoke effect</Text></Message><Message Date="25.06.2011" Time="18:25:35" DateTime="2011-06-25T16:25:35.803Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey whats new??</Text></Message><Message Date="25.06.2011" Time="18:25:52" DateTime="2011-06-25T16:25:52.573Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey man, almost finished with our animated short</Text></Message><Message Date="25.06.2011" Time="18:26:00" DateTime="2011-06-25T16:26:00.413Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">just waiting for someone to add sound to it :)</Text></Message><Message Date="25.06.2011" Time="18:26:02" DateTime="2011-06-25T16:26:02.878Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">what about you?</Text></Message><Message Date="25.06.2011" Time="18:26:34" DateTime="2011-06-25T16:26:34.736Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">same ole... still trying to get better at modeling, and animation learning is very slow.</Text></Message><Message Date="25.06.2011" Time="18:27:11" DateTime="2011-06-25T16:27:11.934Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah, made anything new?</Text></Message><Message Date="25.06.2011" Time="18:27:19" DateTime="2011-06-25T16:27:19.662Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">still working with max?</Text></Message><Message Date="25.06.2011" Time="18:27:43" DateTime="2011-06-25T16:27:43.302Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i dont think i will jump the max ship just yet.</Text></Message><Message Date="25.06.2011" Time="18:28:16" DateTime="2011-06-25T16:28:16.509Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe</Text></Message><Message Date="25.06.2011" Time="18:28:46" DateTime="2011-06-25T16:28:46.509Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">if you really want to learn animation, you should definetly check out tutorials from Keith Lango</Text></Message><Message Date="25.06.2011" Time="18:29:04" DateTime="2011-06-25T16:29:04.270Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that's character animation</Text></Message><Message Date="25.06.2011" Time="18:29:26" DateTime="2011-06-25T16:29:26.994Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i tried, but i could not find anything.</Text></Message><Message Date="25.06.2011" Time="18:29:58" DateTime="2011-06-25T16:29:58.899Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">texture of a model i am doing in mudbox http://www.fox3dmodels.com/WIP/Texture.jpg nudity in it</Text></Message><Message Date="25.06.2011" Time="18:30:04" DateTime="2011-06-25T16:30:04.269Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">dude: http://cgpeers.com</Text></Message><Message Date="25.06.2011" Time="18:30:21" DateTime="2011-06-25T16:30:21.996Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">damn, looks nice!</Text></Message><Message Date="25.06.2011" Time="18:30:28" DateTime="2011-06-25T16:30:28.924Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">mudbox is awesome for texturing</Text></Message><Message Date="25.06.2011" Time="18:30:49" DateTime="2011-06-25T16:30:49.622Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i am really enjoying messing around with it.</Text></Message><Message Date="25.06.2011" Time="18:30:49" DateTime="2011-06-25T16:30:49.934Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cgpeers is THE best torrent-site for cg/3d related stuff</Text></Message><Message Date="25.06.2011" Time="18:31:04" DateTime="2011-06-25T16:31:04.790Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i have an account on that site</Text></Message><Message Date="25.06.2011" Time="18:31:13" DateTime="2011-06-25T16:31:13.932Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">there's some keith lango tuts there too</Text></Message><Message Date="25.06.2011" Time="18:31:18" DateTime="2011-06-25T16:31:18.102Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i did not know his tuts where on the site.</Text></Message><Message Date="25.06.2011" Time="18:31:36" DateTime="2011-06-25T16:31:36.910Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://cgpeers.com/torrents?id=546</Text></Message><Message Date="25.06.2011" Time="18:31:40" DateTime="2011-06-25T16:31:40.125Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://cgpeers.com/torrents?id=348</Text></Message><Message Date="25.06.2011" Time="18:32:00" DateTime="2011-06-25T16:32:00.100Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool thanks i will download thoes now.</Text></Message><Message Date="25.06.2011" Time="18:32:37" DateTime="2011-06-25T16:32:37.541Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">damit for some strange reason budbox keeps crashing.</Text></Message><Message Date="25.06.2011" Time="18:33:02" DateTime="2011-06-25T16:33:02.782Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you'r not running the 32bits?</Text></Message><Message Date="25.06.2011" Time="18:33:11" DateTime="2011-06-25T16:33:11.507Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no 64bit</Text></Message><Message Date="25.06.2011" Time="18:33:33" DateTime="2011-06-25T16:33:33.645Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">weird, i've never had a crash on mudbox</Text></Message><Message Date="25.06.2011" Time="18:33:54" DateTime="2011-06-25T16:33:54.428Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah it just stated crashing today,</Text></Message><Message Date="25.06.2011" Time="18:34:08" DateTime="2011-06-25T16:34:08.222Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you're not running too high res on the textures so that the ram goes empty?</Text></Message><Message Date="25.06.2011" Time="18:34:57" DateTime="2011-06-25T16:34:57.525Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no i have not even started tapping into all my memory.</Text></Message><Message Date="25.06.2011" Time="18:35:12" DateTime="2011-06-25T16:35:12.705Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i will try and uninstall it and reinstall.</Text></Message><Message Date="25.06.2011" Time="18:35:18" DateTime="2011-06-25T16:35:18.869Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">see if that helps.</Text></Message><Message Date="25.06.2011" Time="18:35:32" DateTime="2011-06-25T16:35:32.963Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oh his tuts are for maya.</Text></Message><Message Date="25.06.2011" Time="18:36:06" DateTime="2011-06-25T16:36:06.798Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, but that doesn't matter.. you can apply the principles to 3ds max too</Text></Message><Message Date="25.06.2011" Time="18:37:07" DateTime="2011-06-25T16:37:07.704Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cool i really hope i can get something out of it.</Text></Message><Message Date="25.06.2011" Time="18:37:36" DateTime="2011-06-25T16:37:36.826Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i have way too many short stories i want to do, but because i cant animate it makes it had to do them.</Text></Message><Message Date="25.06.2011" Time="18:37:41" DateTime="2011-06-25T16:37:41.999Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ey do you wanna take a look at the breakdown i put together for the short i've been working on?</Text></Message><Message Date="25.06.2011" Time="18:37:57" DateTime="2011-06-25T16:37:57.909Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure link me.</Text></Message><Message Date="25.06.2011" Time="18:38:13" DateTime="2011-06-25T16:38:13.806Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, understandable :P you gotta just set your mind to one story</Text></Message><Message Date="25.06.2011" Time="18:38:19" DateTime="2011-06-25T16:38:19.726Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://vimeo.com/24747009</Text></Message><Message Date="25.06.2011" Time="18:38:26" DateTime="2011-06-25T16:38:26.461Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">password: fortheking</Text></Message><Message Date="25.06.2011" Time="18:39:09" DateTime="2011-06-25T16:39:09.724Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i dont know if i have ever asked you but do you know how to animate a ship going to warp speed?</Text></Message><Message Date="25.06.2011" Time="18:42:33" DateTime="2011-06-25T16:42:33.100Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">well, i'm not much of a scifi-dude, so i'm pretty sure i've seen that in star-trek or something</Text></Message><Message Date="25.06.2011" Time="18:42:39" DateTime="2011-06-25T16:42:39.565Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">where everything just get blurred out</Text></Message><Message Date="25.06.2011" Time="18:43:18" DateTime="2011-06-25T16:43:18.605Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">everything except the spaceship</Text></Message><Message Date="25.06.2011" Time="18:45:37" DateTime="2011-06-25T16:45:37.870Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">well i am trying to create one like in this video. </Text></Message><Message Date="25.06.2011" Time="18:45:47" DateTime="2011-06-25T16:45:47.278Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hold on my laptop is moving very slow</Text></Message><Message Date="25.06.2011" Time="18:47:08" DateTime="2011-06-25T16:47:08.705Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://www.youtube.com/watch?v=63h5FizGOqk&amp;feature=player_profilepage</Text></Message><Message Date="25.06.2011" Time="18:47:51" DateTime="2011-06-25T16:47:51.278Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that shouln't be to hard to animate</Text></Message><Message Date="25.06.2011" Time="18:48:07" DateTime="2011-06-25T16:48:07.917Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah so it looks</Text></Message><Message Date="25.06.2011" Time="18:48:30" DateTime="2011-06-25T16:48:30.360Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i can get the flyby part, but the part where it takes off really fast i cant do.</Text></Message><Message Date="25.06.2011" Time="18:48:59" DateTime="2011-06-25T16:48:59.453Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i think you could do it by bluring the first frame when it goes to warp-speed, and then add some camerashake</Text></Message><Message Date="25.06.2011" Time="18:49:03" DateTime="2011-06-25T16:49:03.941Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I have been trying to find out how to do that for months with out ant help</Text></Message><Message Date="25.06.2011" Time="18:49:42" DateTime="2011-06-25T16:49:42.463Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hmmm but how do you make it take off fast</Text></Message><Message Date="25.06.2011" Time="18:49:49" DateTime="2011-06-25T16:49:49.581Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">in the video it actually looks like he's animating the spaceship in the wrong direction right before it takes of</Text></Message><Message Date="25.06.2011" Time="18:49:55" DateTime="2011-06-25T16:49:55.294Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">like one frame or so</Text></Message><Message Date="25.06.2011" Time="18:50:30" DateTime="2011-06-25T16:50:30.989Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you know, when setting keyframes, you can adjust the curve-interpolation </Text></Message><Message Date="25.06.2011" Time="18:50:39" DateTime="2011-06-25T16:50:39.389Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">that's what you need to do</Text></Message><Message Date="25.06.2011" Time="18:50:46" DateTime="2011-06-25T16:50:46.094Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">adjust the animation curves</Text></Message><Message Date="25.06.2011" Time="18:51:18" DateTime="2011-06-25T16:51:18.590Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i tired that in max, but i just did not understand it.</Text></Message><Message Date="25.06.2011" Time="18:51:47" DateTime="2011-06-25T16:51:47.142Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i try add more keyframes to slow it down, and spacing the frames to speed it up, but it never works out for mr.</Text></Message><Message Date="25.06.2011" Time="18:51:48" DateTime="2011-06-25T16:51:48.569Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">me</Text></Message><Message Date="25.06.2011" Time="18:51:54" DateTime="2011-06-25T16:51:54.622Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">it isn't to hard to wrap your mind around, check out if there's some tuts on youtube</Text></Message><Message Date="25.06.2011" Time="18:52:32" DateTime="2011-06-25T16:52:32.175Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i'm pretty sure you can do that with 2 keyframes, then just adjust the curves to acheive the result you're looking for</Text></Message><Message Date="25.06.2011" Time="18:52:48" DateTime="2011-06-25T16:52:48.905Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I did i have even have cg tutors videos on max curv editor and i still cant get it.</Text></Message><Message Date="25.06.2011" Time="18:53:33" DateTime="2011-06-25T16:53:33.312Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">by the way awsome video you have there.</Text></Message><Message Date="25.06.2011" Time="18:53:36" DateTime="2011-06-25T16:53:36.686Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">you can't settle with that, you just gotta try to get it until you get it :)</Text></Message><Message Date="25.06.2011" Time="18:53:48" DateTime="2011-06-25T16:53:48.201Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i cant see the story is coming along nicely.</Text></Message><Message Date="25.06.2011" Time="18:54:00" DateTime="2011-06-25T16:54:00.863Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hehe i guess</Text></Message><Message Date="25.06.2011" Time="18:54:16" DateTime="2011-06-25T16:54:16.030Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks</Text></Message><Message Date="25.06.2011" Time="18:54:24" DateTime="2011-06-25T16:54:24.965Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but i get really stress when i try it for a few days and i cant see any improvments</Text></Message><Message Date="25.06.2011" Time="18:55:14" DateTime="2011-06-25T16:55:14.281Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I wish i had some one to show me how to do that effect in max so i can really wrap my head around it.</Text></Message><Message Date="25.06.2011" Time="18:56:03" DateTime="2011-06-25T16:56:03.175Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Are you ever going to do an kung fu type animations?</Text></Message><Message Date="25.06.2011" Time="18:57:58" DateTime="2011-06-25T16:57:58.669Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hell yeah, that's something i really really wanna do.. my plan is to create two robot's, rig them, and make'em fight an epic battle ;)</Text></Message><Message Date="25.06.2011" Time="18:58:44" DateTime="2011-06-25T16:58:44.830Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah thats what i want to do with my short story called chasing death.</Text></Message><Message Date="25.06.2011" Time="18:59:05" DateTime="2011-06-25T16:59:05.877Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">But how will you go about working out the fight scene?</Text></Message><Message Date="25.06.2011" Time="19:00:06" DateTime="2011-06-25T17:00:06.078Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i just want to animate without having to think of a story at all, just the fighting.. i'm gonna collect toons of reference material, like from parkour, kung fu, athletics, movies like undisputed 2 &amp; 3, the work my way from there</Text></Message><Message Date="25.06.2011" Time="19:01:00" DateTime="2011-06-25T17:01:00.715Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah have i ever shown you the kung fu love video i found online?</Text></Message><Message Date="25.06.2011" Time="19:01:11" DateTime="2011-06-25T17:01:11.700Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i really like what thoes guys did.</Text></Message><Message Date="25.06.2011" Time="19:01:20" DateTime="2011-06-25T17:01:20.205Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">not sure, link me</Text></Message><Message Date="25.06.2011" Time="19:02:11" DateTime="2011-06-25T17:02:11.363Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://vimeo.com/4334717</Text></Message><Message Date="25.06.2011" Time="19:03:15" DateTime="2011-06-25T17:03:15.687Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i also like the models really sexy ninja babes.</Text></Message><Message Date="25.06.2011" Time="19:03:43" DateTime="2011-06-25T17:03:43.582Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">awesome</Text></Message><Message Date="25.06.2011" Time="19:04:20" DateTime="2011-06-25T17:04:20.834Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah ever since i saw that video i was like i have to do one like it.</Text></Message><Message Date="25.06.2011" Time="19:04:40" DateTime="2011-06-25T17:04:40.285Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but thanks ot my limitations i cant :(</Text></Message><Message Date="25.06.2011" Time="19:05:48" DateTime="2011-06-25T17:05:48.988Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">just take a stab at it.. just start, and learn while doing, that's best way to learn man</Text></Message><Message Date="25.06.2011" Time="19:06:57" DateTime="2011-06-25T17:06:57.528Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah but it is hard to find some of the learning videos or tuts online to solve some of the problems you run into, and not a lot of ppl like to shear what they know.</Text></Message><Message Date="25.06.2011" Time="19:07:48" DateTime="2011-06-25T17:07:48.013Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">as long as you know what you want to learn, there is tutorials on it</Text></Message><Message Date="25.06.2011" Time="19:08:27" DateTime="2011-06-25T17:08:27.934Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but ofcourse, you won't find a tutorial where guys learn you the whole process for exactly what you're gonna do</Text></Message><Message Date="25.06.2011" Time="19:08:52" DateTime="2011-06-25T17:08:52.206Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">true</Text></Message><Message Date="25.06.2011" Time="19:10:22" DateTime="2011-06-25T17:10:22.859Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Well i will nto take up anymore of your time. But please let me know what you are going to get started on your kung fu story i would really like to see some of your steps in creating it.</Text></Message><Message Date="25.06.2011" Time="19:11:15" DateTime="2011-06-25T17:11:15.085Z" SessionID="68"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure, it's gonna take some time though.. a lot of things happening at the moment, but i'll give you a peep when i eventually get started :)</Text></Message><Message Date="25.06.2011" Time="19:12:04" DateTime="2011-06-25T17:12:04.055Z" SessionID="68"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jh"/></To><Text Style="font-family:Segoe UI; color:#000000; ">nice.</Text></Message><Message Date="03.08.2011" Time="00:26:53" DateTime="2011-08-02T22:26:53.433Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">what's new?</Text></Message><Message Date="03.08.2011" Time="00:27:08" DateTime="2011-08-02T22:27:08.606Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey, just released our short film :)</Text></Message><Message Date="03.08.2011" Time="00:27:16" DateTime="2011-08-02T22:27:16.746Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">sweet</Text></Message><Message Date="03.08.2011" Time="00:27:26" DateTime="2011-08-02T22:27:26.046Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://forums.cgsociety.org/showthread.php?p=7069069#post7069069 if you wanna check it out</Text></Message><Message Date="03.08.2011" Time="00:27:26" DateTime="2011-08-02T22:27:26.919Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">do yiy have a link to it?</Text></Message><Message Date="03.08.2011" Time="00:32:26" DateTime="2011-08-02T22:32:26.645Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">wowwiw looks really good.</Text></Message><Message Date="03.08.2011" Time="00:34:30" DateTime="2011-08-02T22:34:30.265Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">animation is really smooth</Text></Message><Message Date="03.08.2011" Time="00:34:42" DateTime="2011-08-02T22:34:42.734Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks alot man :)</Text></Message><Message Date="03.08.2011" Time="00:34:48" DateTime="2011-08-02T22:34:48.999Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">lol they killed the king</Text></Message><Message Date="03.08.2011" Time="00:35:49" DateTime="2011-08-02T22:35:49.527Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">checking out for the king breakdown.</Text></Message><Message Date="03.08.2011" Time="00:36:19" DateTime="2011-08-02T22:36:19.001Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">ahhh this ship model I am doing is coming along so slow.</Text></Message><Message Date="03.08.2011" Time="00:36:37" DateTime="2011-08-02T22:36:37.151Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">still working on the camera animation?</Text></Message><Message Date="03.08.2011" Time="00:37:14" DateTime="2011-08-02T22:37:14.582Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">kinda on hold until I can finish up the ship.</Text></Message><Message Date="03.08.2011" Time="00:37:46" DateTime="2011-08-02T22:37:46.751Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i see, well i've just started out on my samurai/robot-thingy</Text></Message><Message Date="03.08.2011" Time="00:37:54" DateTime="2011-08-02T22:37:54.967Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">dude tour rigs are really cool.</Text></Message><Message Date="03.08.2011" Time="00:38:07" DateTime="2011-08-02T22:38:07.133Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks :)</Text></Message><Message Date="03.08.2011" Time="00:38:44" DateTime="2011-08-02T22:38:44.557Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">would you like to see what I have of the ship.</Text></Message><Message Date="03.08.2011" Time="00:39:06" DateTime="2011-08-02T22:39:06.670Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">sure</Text></Message><Message Date="03.08.2011" Time="00:39:49" DateTime="2011-08-02T22:39:49.076Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">k one sec. do you have the concept of his tour ninja guy will look?</Text></Message><Message Date="03.08.2011" Time="00:40:39" DateTime="2011-08-02T22:40:39.774Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">well, no, i'm just modeling straight ahead :P</Text></Message><Invitation Date="03.08.2011" Time="00:41:00" DateTime="2011-08-02T22:41:00.798Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><File>C:\Users\<USER>\Desktop\Untitled-1 copy.jpg</File><Text Style="color:#545454; ">Jørn sender C:\Users\<USER>\Desktop\Untitled-1 copy.jpg</Text></Invitation><InvitationResponse Date="03.08.2011" Time="00:41:01" DateTime="2011-08-02T22:41:01.311Z" SessionID="69"><From><User FriendlyName="Fox"/></From><File>C:\Users\<USER>\Desktop\Untitled-1 copy.jpg</File><Text Style="color:#800000; ">Kan ikke sende "Untitled-1 copy.jpg" til Fox.</Text></InvitationResponse><Invitation Date="03.08.2011" Time="00:41:13" DateTime="2011-08-02T22:41:13.195Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><File>C:\Users\<USER>\Desktop\Untitled-1 copy.jpg</File><Text Style="color:#545454; ">Jørn sender C:\Users\<USER>\Desktop\Untitled-1 copy.jpg</Text></Invitation><InvitationResponse Date="03.08.2011" Time="00:41:13" DateTime="2011-08-02T22:41:13.428Z" SessionID="69"><From><User FriendlyName="Fox"/></From><File>C:\Users\<USER>\Desktop\Untitled-1 copy.jpg</File><Text Style="color:#800000; ">Kan ikke sende "Untitled-1 copy.jpg" til Fox.</Text></InvitationResponse><Message Date="03.08.2011" Time="00:41:18" DateTime="2011-08-02T22:41:18.700Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">lol yeah I do that a lit.</Text></Message><Message Date="03.08.2011" Time="00:42:41" DateTime="2011-08-02T22:42:41.630Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">I am also trying to storybord out my short story called chasing death I finally complete the script on it. I need to re-write it to iron out some stuff but the basic on what I want is in the script.</Text></Message><Message Date="03.08.2011" Time="00:44:57" DateTime="2011-08-02T22:44:57.964Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">http://www.fox3dmodels.com/WIP/ShipTop2.jpg</Text></Message><Message Date="03.08.2011" Time="00:45:35" DateTime="2011-08-02T22:45:35.919Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">looks nice :)</Text></Message><Message Date="03.08.2011" Time="00:45:40" DateTime="2011-08-02T22:45:40.915Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">I am trying to get it as detail as I can.</Text></Message><Message Date="03.08.2011" Time="00:47:21" DateTime="2011-08-02T22:47:21.937Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">what are your steps for creating your story?</Text></Message><Message Date="03.08.2011" Time="00:47:55" DateTime="2011-08-02T22:47:55.983Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">don't really got any steps to it, just brainstorming actually</Text></Message><Message Date="03.08.2011" Time="00:48:15" DateTime="2011-08-02T22:48:15.440Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">oh OK.</Text></Message><Message Date="03.08.2011" Time="00:48:33" DateTime="2011-08-02T22:48:33.664Z" SessionID="69"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and when you get a good idea, try to write it down and refine it, write down every single detail</Text></Message><Message Date="03.08.2011" Time="00:49:08" DateTime="2011-08-02T22:49:08.193Z" SessionID="69"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">yeah that's what I did for my chasing death story.</Text></Message><Message Date="08.08.2011" Time="00:10:40" DateTime="2011-08-07T22:10:40.745Z" SessionID="70"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://www.fox3dmodels.com/WIP/ShipTop3.jpg</Text></Message><Message Date="20.08.2011" Time="16:42:55" DateTime="2011-08-20T14:42:55.600Z" SessionID="71"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey whats new?</Text></Message><Message Date="18.02.2012" Time="01:22:10" DateTime="2012-02-18T00:22:10.052Z" SessionID="72"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">hey what's new?</Text></Message><Message Date="18.02.2012" Time="01:22:49" DateTime="2012-02-18T00:22:49.060Z" SessionID="72"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">hey man, just going to bed, talk to ya tomorrow :)</Text></Message><Message Date="18.02.2012" Time="01:22:50" DateTime="2012-02-18T00:22:50.676Z" SessionID="72"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cheers</Text></Message><Message Date="18.02.2012" Time="01:23:00" DateTime="2012-02-18T00:23:00.808Z" SessionID="73"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="color:windowtext; ">k</Text></Message><Message Date="07.04.2012" Time="23:47:21" DateTime="2012-04-07T21:47:21.221Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Ahhh i like the new max to maya setup with cat rig</Text></Message><Message Date="07.04.2012" Time="23:52:39" DateTime="2012-04-07T21:52:39.453Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Hey man, what's that?</Text></Message><Message Date="07.04.2012" Time="23:53:15" DateTime="2012-04-07T21:53:15.724Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">cat rig for both maya and max</Text></Message><Message Date="07.04.2012" Time="23:53:36" DateTime="2012-04-07T21:53:36.965Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">now you can send maya rigs to max and it will work fine</Text></Message><Message Date="07.04.2012" Time="23:53:57" DateTime="2012-04-07T21:53:57.597Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Is it the HIK you're talking about?</Text></Message><Message Date="07.04.2012" Time="23:54:02" DateTime="2012-04-07T21:54:02.173Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">HumanIK</Text></Message><Message Date="07.04.2012" Time="23:55:47" DateTime="2012-04-07T21:55:47.889Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">no cat rig which was in max, but now it is also in maya 2013</Text></Message><Message Date="07.04.2012" Time="23:56:22" DateTime="2012-04-07T21:56:22.253Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Ok, so it's not like you can have a custom rig in maya and bring it back into max?</Text></Message><Message Date="07.04.2012" Time="23:56:44" DateTime="2012-04-07T21:56:44.765Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">didn't think maya and max used the same kind of constraints and parents</Text></Message><Message Date="07.04.2012" Time="23:57:49" DateTime="2012-04-07T21:57:49.987Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yes you can create your own custome rigs in CAT</Text></Message><Message Date="07.04.2012" Time="23:58:04" DateTime="2012-04-07T21:58:04.317Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">character animation toolkit?</Text></Message><Message Date="07.04.2012" Time="23:58:06" DateTime="2012-04-07T21:58:06.755Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but now you can export any cat rigs you create in maya to max</Text></Message><Message Date="07.04.2012" Time="23:58:11" DateTime="2012-04-07T21:58:11.524Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yes</Text></Message><Message Date="07.04.2012" Time="23:58:46" DateTime="2012-04-07T21:58:46.013Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Gotta check that out, do got any sources or tutorials on this? </Text></Message><Message Date="07.04.2012" Time="23:58:53" DateTime="2012-04-07T21:58:53.004Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">http://www.youtube.com/watch?v=dr6CFmocxW8</Text></Message><Message Date="08.04.2012" Time="00:00:25" DateTime="2012-04-07T22:00:25.604Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">oops wrong tut</Text></Message><Message Date="08.04.2012" Time="00:00:33" DateTime="2012-04-07T22:00:33.651Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">let me find the maya tut, but thats CAT</Text></Message><Message Date="08.04.2012" Time="00:00:39" DateTime="2012-04-07T22:00:39.053Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">which is also now in Maya</Text></Message><Message Date="08.04.2012" Time="00:00:56" DateTime="2012-04-07T22:00:56.109Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I guess CAT and HIK is the same thing</Text></Message><Message Date="08.04.2012" Time="00:01:09" DateTime="2012-04-07T22:01:09.150Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I think it is</Text></Message><Message Date="08.04.2012" Time="00:01:53" DateTime="2012-04-07T22:01:53.734Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">But i like the fact now you can move your max rigs create in CAT into maya, and work on them, or send the rig to max</Text></Message><Message Date="08.04.2012" Time="00:03:12" DateTime="2012-04-07T22:03:12.781Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Yeah, that's pretty sweet :) I saw some new stuff on the HIK stuff in Maya, which makes it possible to add mocap ontop of custom rigs, so that you actually get the mocap as editable keyframes on your custom control-rig</Text></Message><Message Date="08.04.2012" Time="00:03:45" DateTime="2012-04-07T22:03:45.282Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i really would like to mess with it.</Text></Message><Message Date="08.04.2012" Time="00:04:06" DateTime="2012-04-07T22:04:06.249Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">So what have you been up too?</Text></Message><Message Date="08.04.2012" Time="00:04:34" DateTime="2012-04-07T22:04:34.960Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">are you still teaching?</Text></Message><Message Date="08.04.2012" Time="00:06:30" DateTime="2012-04-07T22:06:30.397Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yep, still teaching :) don't have too much spare time for private projects between the lecturing</Text></Message><Message Date="08.04.2012" Time="00:06:39" DateTime="2012-04-07T22:06:39.389Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">and you?</Text></Message><Message Date="08.04.2012" Time="00:07:25" DateTime="2012-04-07T22:07:25.458Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">still working as an IT guy, and dont have any time at all to work on my models, or redo the rig i messed up.</Text></Message><Message Date="08.04.2012" Time="00:10:31" DateTime="2012-04-07T22:10:31.117Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ah yes, it's really hard to combine the work with 3d, I hear ya.. when I left IT and went to school to learn 3d, I was blown away with how fast I learned stuff, the main reason was the time, 3d is extremely time-consuming to learn</Text></Message><Message Date="08.04.2012" Time="00:11:15" DateTime="2012-04-07T22:11:15.980Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yup</Text></Message><Message Date="08.04.2012" Time="00:12:43" DateTime="2012-04-07T22:12:43.765Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I am thinking about going back to school to learn 3d. Work is starting to get to me, and it is not what i enjoy doing.</Text></Message><Message Date="08.04.2012" Time="00:14:45" DateTime="2012-04-07T22:14:45.997Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">do it man, the fact that you've been working is really a good thing when you go back to school, it makes you work harder - and to know what you don't wanna do is extra motivation to kick ass to make it in something you want to work with</Text></Message><Message Date="08.04.2012" Time="00:14:51" DateTime="2012-04-07T22:14:51.469Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">how old are you?</Text></Message><Message Date="08.04.2012" Time="00:16:04" DateTime="2012-04-07T22:16:04.726Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i just turned 34 yeah i'm getting old thats why i want to do this before another year passes me by doing a job i am not happy with.</Text></Message><Message Date="08.04.2012" Time="00:17:09" DateTime="2012-04-07T22:17:09.646Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">exactly, the age and general experience is really a good thing when it comes down to it</Text></Message><Message Date="08.04.2012" Time="00:18:48" DateTime="2012-04-07T22:18:48.989Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I hesitated like hell, for five years or so.. but the experience with working with something I started to hate, really made it fun to work my ass off with 3d</Text></Message><Message Date="08.04.2012" Time="00:19:43" DateTime="2012-04-07T22:19:43.244Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah i like creating, and my job does not give me that freedom.</Text></Message><Message Date="08.04.2012" Time="00:20:55" DateTime="2012-04-07T22:20:55.805Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">same thing here, combined with a really stressful job :P I thought about trying out programming also, but it was hard to get started with it </Text></Message><Message Date="08.04.2012" Time="00:21:18" DateTime="2012-04-07T22:21:18.449Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">me too</Text></Message><Message Date="08.04.2012" Time="00:21:54" DateTime="2012-04-07T22:21:54.372Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but i like doing 3d even if right now i am not good at it i like that fact that i can create anything i can think up.</Text></Message><Message Date="08.04.2012" Time="00:22:02" DateTime="2012-04-07T22:22:02.829Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">also, coming from IT, you'll grasp the technical aspect of 3d pretty easy</Text></Message><Message Date="08.04.2012" Time="00:22:18" DateTime="2012-04-07T22:22:18.493Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah</Text></Message><Message Date="08.04.2012" Time="00:25:05" DateTime="2012-04-07T22:25:05.763Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">I know my way around 3ds max, but i need some one to help me to get a hang on how to create complex model fast.</Text></Message><Message Date="08.04.2012" Time="00:25:16" DateTime="2012-04-07T22:25:16.544Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">So going back to school will help me.</Text></Message><Message Date="08.04.2012" Time="00:26:26" DateTime="2012-04-07T22:26:26.461Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">Have you tried out zbrush?</Text></Message><Message Date="08.04.2012" Time="00:26:58" DateTime="2012-04-07T22:26:58.486Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah it is cool, but i'm falling in love with mudbox.</Text></Message><Message Date="08.04.2012" Time="00:29:19" DateTime="2012-04-07T22:29:19.773Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">yeah, I like mudbox for texture and zbrush for sculpting, matter of preference :)</Text></Message><Message Date="08.04.2012" Time="00:29:25" DateTime="2012-04-07T22:29:25.726Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">but I gotta get some sleep</Text></Message><Message Date="08.04.2012" Time="00:29:53" DateTime="2012-04-07T22:29:53.029Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">ok see you later dude always nice chatting with you.</Text></Message><Message Date="08.04.2012" Time="00:30:10" DateTime="2012-04-07T22:30:10.173Z" SessionID="74"><From><User FriendlyName="Jørn"/></From><To><User FriendlyName="Fox"/></To><Text Style="font-family:Segoe UI; color:#000000; ">thanks the same, see ya later, update me on which school you'll choose ;) </Text></Message><Message Date="08.04.2012" Time="00:30:19" DateTime="2012-04-07T22:30:19.348Z" SessionID="74"><From><User FriendlyName="Fox"/></From><To><User FriendlyName="Jørn"/></To><Text Style="font-family:Segoe UI; color:#000000; ">i will</Text></Message></Log>